#!/usr/bin/env python3
"""
FastAPI server for Piper TTS using the official Piper implementation.
"""

import io
import logging
import wave
import asyncio
from pathlib import Path
from typing import Optional, List
from concurrent.futures import Thread<PERSON>oolExecutor

from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

try:
    from .voice import PiperVoice
    from .model_manager import Model<PERSON>anager
    from .voice_cloning_endpoints import add_voice_cloning_endpoints, voice_cloning_manager
except ImportError:
    from voice import <PERSON>Voice
    from model_manager import Model<PERSON>anager
    from voice_cloning_endpoints import add_voice_cloning_endpoints, voice_cloning_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Piper TTS API",
    description="Text-to-Speech API using official Piper implementation with speaker and speech rate control",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class VoiceManager:
    """Optimized voice manager for scalable inference"""
    def __init__(self):
        self.voice: Optional[PiperVoice] = None
        self.voice_config = {}
        self.is_loading = False
        self.model_manager = ModelManager("models")

    def load_voice(self, model_path: str, config_path: Optional[str] = None, use_cuda: bool = False):
        """Load voice with optimization for inference"""
        if self.is_loading:
            return False

        self.is_loading = True
        try:
            # Load with optimized ONNX session options
            self.voice = PiperVoice.load(model_path, config_path=config_path, use_cuda=use_cuda)
            self.voice_config = {
                "model_path": model_path,
                "config_path": config_path or f"{model_path}.json",
                "use_cuda": use_cuda
            }
            logger.info(f"Voice loaded: speakers={self.voice.config.num_speakers}, sample_rate={self.voice.config.sample_rate}")
            return True
        except Exception as e:
            logger.error(f"Failed to load voice: {e}")
            return False
        finally:
            self.is_loading = False

    def synthesize_audio(self, text: str, speaker_id: Optional[int] = None,
                        length_scale: Optional[float] = None, noise_scale: Optional[float] = None,
                        noise_w: Optional[float] = None, sentence_silence: float = 0.0) -> bytes:
        """Optimized synthesis function"""
        if not self.voice:
            raise ValueError("No voice loaded")

        with io.BytesIO() as wav_io:
            with wave.open(wav_io, "wb") as wav_file:
                self.voice.synthesize(
                    text=text,
                    wav_file=wav_file,
                    speaker_id=speaker_id,
                    length_scale=length_scale,
                    noise_scale=noise_scale,
                    noise_w=noise_w,
                    sentence_silence=sentence_silence
                )
            return wav_io.getvalue()

# Global voice manager and thread pool for CPU-bound inference
voice_manager = VoiceManager()
executor = ThreadPoolExecutor(max_workers=2)  # Limit to prevent memory issues with ONNX

# Pydantic models for request/response
class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=2000)
    speaker_id: Optional[int] = Field(None, description="Speaker ID", ge=0)
    speech_rate: Optional[float] = Field(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0)
    noise_scale: Optional[float] = Field(None, description="Generator noise (0.0-1.0)")
    noise_w: Optional[float] = Field(None, description="Phoneme width noise (0.0-1.0)")
    sentence_silence: Optional[float] = Field(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)

class VoiceInfo(BaseModel):
    num_speakers: int
    sample_rate: int
    espeak_voice: str
    phoneme_type: str
    length_scale: float
    noise_scale: float
    noise_w: float

class SpeakerInfo(BaseModel):
    speaker_id: int
    total_speakers: int

# Remove old load_voice function - now handled by VoiceManager

@app.on_event("startup")
async def startup_event():
    """Initialize the voice on startup."""
    # Get the default model from model manager
    default_model_id = voice_manager.model_manager.get_default_model_id()

    if default_model_id:
        model_paths = voice_manager.model_manager.get_model_paths(default_model_id)
        if model_paths:
            model_path, config_path = model_paths
            logger.info(f"Loading default model: {default_model_id}")
            if voice_manager.load_voice(model_path, config_path):
                voice_manager.model_manager.set_current_model(default_model_id)
                logger.info(f"Successfully loaded default model: {default_model_id}")
            else:
                logger.warning(f"Failed to load default model: {default_model_id}")
        else:
            logger.warning(f"Model paths not found for: {default_model_id}")
    else:
        logger.warning("No models found. Add models to the 'models' directory.")

@app.get("/", summary="Health check")
async def root():
    """Health check endpoint."""
    current_model = voice_manager.model_manager.get_current_model()
    return {
        "message": "Piper TTS API is running",
        "status": "healthy",
        "voice_loaded": voice_manager.voice is not None,
        "current_model": current_model.name if current_model else None,
        "available_models": len(voice_manager.model_manager.available_models)
    }

@app.get("/models", summary="Get available models")
async def get_available_models():
    """Get list of all available models."""
    return {
        "models": voice_manager.model_manager.get_available_models(),
        "current_model": voice_manager.model_manager.current_model_id
    }

@app.post("/models/{model_id}/load", summary="Load a specific model")
async def load_model(
    model_id: str,
    use_cuda: bool = Query(False, description="Use CUDA acceleration")
):
    """Load a specific model by ID."""
    model_paths = voice_manager.model_manager.get_model_paths(model_id)
    if not model_paths:
        raise HTTPException(status_code=404, detail=f"Model not found: {model_id}")

    model_path, config_path = model_paths
    success = voice_manager.load_voice(model_path, config_path, use_cuda)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to load model: {model_id}")

    voice_manager.model_manager.set_current_model(model_id)
    model_info = voice_manager.model_manager.get_model_info(model_id)

    return {
        "message": f"Model '{model_info.name}' loaded successfully",
        "model_id": model_id,
        "model_info": {
            "name": model_info.name,
            "description": model_info.description,
            "type": model_info.type,
            "quality": model_info.quality,
            "speakers": model_info.speakers,
            "sample_rate": model_info.sample_rate
        },
        "voice_info": await get_voice_info()
    }

@app.post("/models/refresh", summary="Refresh available models")
async def refresh_models():
    """Refresh the list of available models."""
    old_count = len(voice_manager.model_manager.available_models)
    voice_manager.model_manager.refresh_models()
    new_count = len(voice_manager.model_manager.available_models)

    return {
        "message": "Models refreshed successfully",
        "models_found": new_count,
        "models_added": new_count - old_count,
        "models": voice_manager.model_manager.get_available_models()
    }

@app.get("/models/stats", summary="Get model statistics")
async def get_model_stats():
    """Get statistics about available models."""
    return voice_manager.model_manager.get_model_stats()

@app.get("/models/check-new", summary="Check for new models")
async def check_new_models():
    """Check if there are new models available."""
    has_new = voice_manager.model_manager.has_new_models()
    return {
        "has_new_models": has_new,
        "message": "New models detected" if has_new else "No new models found",
        "last_scan": voice_manager.model_manager.last_scan_time
    }

@app.post("/models/auto-scan/{action}", summary="Control auto-scanning")
async def control_auto_scan(action: str):
    """Start or stop automatic model scanning."""
    if action.lower() == "start":
        voice_manager.model_manager.start_auto_scan()
        return {"message": "Auto-scanning started", "status": "enabled"}
    elif action.lower() == "stop":
        voice_manager.model_manager.stop_auto_scan()
        return {"message": "Auto-scanning stopped", "status": "disabled"}
    else:
        raise HTTPException(status_code=400, detail="Action must be 'start' or 'stop'")

@app.post("/load_voice", summary="Load a voice model (legacy)")
async def load_voice_endpoint(
    model_path: str = Query(..., description="Path to ONNX model file"),
    config_path: Optional[str] = Query(None, description="Path to config JSON file"),
    use_cuda: bool = Query(False, description="Use CUDA acceleration")
):
    """Load a voice model from the specified path (legacy endpoint)."""
    if not Path(model_path).exists():
        raise HTTPException(status_code=404, detail=f"Model file not found: {model_path}")

    if config_path and not Path(config_path).exists():
        raise HTTPException(status_code=404, detail=f"Config file not found: {config_path}")

    success = voice_manager.load_voice(model_path, config_path, use_cuda)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to load voice model")

    return {
        "message": "Voice loaded successfully",
        "model_path": model_path,
        "config_path": config_path or f"{model_path}.json",
        "voice_info": await get_voice_info()
    }

@app.get("/voice/info", response_model=VoiceInfo, summary="Get voice information")
async def get_voice_info():
    """Get information about the loaded voice."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return VoiceInfo(
        num_speakers=voice_manager.voice.config.num_speakers,
        sample_rate=voice_manager.voice.config.sample_rate,
        espeak_voice=voice_manager.voice.config.espeak_voice,
        phoneme_type=voice_manager.voice.config.phoneme_type.value,
        length_scale=voice_manager.voice.config.length_scale,
        noise_scale=voice_manager.voice.config.noise_scale,
        noise_w=voice_manager.voice.config.noise_w
    )

@app.get("/speakers", summary="Get speaker information")
async def get_speakers():
    """Get information about available speakers."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return {
        "num_speakers": voice_manager.voice.config.num_speakers,
        "speakers": [{"speaker_id": i} for i in range(voice_manager.voice.config.num_speakers)]
    }

@app.post("/synthesize", summary="Synthesize speech from text")
async def synthesize_speech(request: TTSRequest):
    """
    Synthesize speech from text with speaker and speech rate control.

    Returns WAV audio data.
    """
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    try:
        logger.info(f"Synthesis request: text='{request.text[:50]}...', speaker_id={request.speaker_id}")

        # Validate speaker ID
        if request.speaker_id is not None:
            if request.speaker_id < 0 or request.speaker_id >= voice_manager.voice.config.num_speakers:
                raise HTTPException(
                    status_code=400,
                    detail=f"Speaker ID must be between 0 and {voice_manager.voice.config.num_speakers - 1}"
                )

        # Convert speech_rate to length_scale (inverse relationship)
        length_scale = 1.0 / request.speech_rate if request.speech_rate else None
        logger.info(f"Synthesis parameters: length_scale={length_scale}, noise_scale={request.noise_scale}")

        # Run synthesis in thread pool for better concurrency
        loop = asyncio.get_event_loop()
        wav_data = await loop.run_in_executor(
            executor,
            voice_manager.synthesize_audio,
            request.text,
            request.speaker_id,
            length_scale,
            request.noise_scale,
            request.noise_w,
            request.sentence_silence or 0.0
        )

        logger.info(f"Generated audio: {len(wav_data)} bytes")

        # Return WAV file
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "Content-Length": str(len(wav_data))
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error during synthesis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during synthesis")

@app.get("/synthesize", summary="Synthesize speech from text (GET)")
async def synthesize_speech_get(
    text: str = Query(..., description="Text to synthesize", min_length=1, max_length=2000),
    speaker_id: Optional[int] = Query(None, description="Speaker ID", ge=0),
    speech_rate: Optional[float] = Query(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0),
    noise_scale: Optional[float] = Query(None, description="Generator noise (0.0-1.0)", ge=0.0, le=1.0),
    noise_w: Optional[float] = Query(None, description="Phoneme width noise (0.0-1.0)", ge=0.0, le=1.0),
    sentence_silence: Optional[float] = Query(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)
):
    """
    Synthesize speech from text using GET parameters.
    
    Returns WAV audio data.
    """
    request = TTSRequest(
        text=text,
        speaker_id=speaker_id,
        speech_rate=speech_rate,
        noise_scale=noise_scale,
        noise_w=noise_w,
        sentence_silence=sentence_silence
    )
    return await synthesize_speech(request)

# Add voice cloning endpoints
try:
    add_voice_cloning_endpoints(app, voice_cloning_manager)
    logger.info("Voice cloning endpoints added successfully")
except Exception as e:
    logger.warning(f"Failed to add voice cloning endpoints: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000,reload=True)
