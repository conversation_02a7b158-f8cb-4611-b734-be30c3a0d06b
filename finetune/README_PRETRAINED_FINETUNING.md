# Fine-tuning with Pretrained Nepali Model

This directory contains a clean setup for fine-tuning a TTS model using the pretrained `speaches-ai/piper-ne_NP-google-medium` model from Hugging Face.

## Current Status
✅ **Pretrained model downloaded** - speaches-ai/piper-ne_NP-google-medium (76.8 MB)
✅ **Dataset prepared** - 2064 Nepali audio files with transcriptions
✅ **Environment ready** - Piper training dependencies installed
🔄 **Ready for training** - All setup complete, ready to start fine-tuning

## Why Use Pretrained Model?

The pretrained model offers several advantages:
- ✅ **Already knows Nepali** - trained on high-quality Nepali data
- ✅ **18 different speakers** - rich voice variety
- ✅ **Professional phoneme mapping** - accurate pronunciation (154 phonemes)
- ✅ **Faster convergence** - less training time needed
- ✅ **Better quality** - superior results compared to training from scratch

## Quick Start

### Option 1: Complete Automated Workflow
```bash
python run_pretrained_finetuning.py
```
This script handles everything automatically:
1. Downloads the pretrained model
2. Prepares your dataset
3. Starts fine-tuning

### Option 2: Step-by-Step Process

1. **Setup pretrained model:**
```bash
python setup_pretrained_finetuning.py
```

2. **Prepare your dataset:**
```bash
python prepare_dataset_for_training.py
```

3. **Start fine-tuning:**
```bash
python train_with_pretrained.py
```

## Prerequisites

### Dataset Format
Your dataset should be organized as:
```
training_data/
├── metadata.csv          # Format: filename|text
└── wav/
    ├── audio1.wav
    ├── audio2.wav
    └── ...
```

### System Requirements
- Python 3.8+
- PyTorch with CUDA support
- At least 8GB GPU memory (RTX 3060 or better)
- 10GB free disk space

## Fine-tuning Parameters

The fine-tuning process uses optimized parameters:
- **Learning Rate**: 0.0001 (lower for stable fine-tuning)
- **Batch Size**: 8 (moderate for RTX 3060)
- **Epochs**: 500 (more epochs for better adaptation)
- **Precision**: 16-mixed (memory efficient)
- **Gradient Clipping**: 1.0 (for stability)

## Monitoring Progress

Monitor training with TensorBoard:
```bash
tensorboard --logdir training_output/lightning_logs
```

Open http://localhost:6006 to view:
- Training/validation loss
- Learning rate schedule
- Audio samples (if available)

## Output Files

After training, you'll find:
- `training_output/lightning_logs/version_X/checkpoints/` - Model checkpoints
- `training_output/lightning_logs/version_X/` - Training logs
- `training_output/config.json` - Training configuration

## Next Steps

1. **Export to ONNX**: Convert the best checkpoint to ONNX format
2. **Test the model**: Use your API to test the fine-tuned model
3. **Deploy**: Replace your current model with the fine-tuned version

## Troubleshooting

### Common Issues

**GPU Memory Error:**
- Reduce batch size to 4 or 2
- Use CPU if GPU memory is insufficient

**Download Fails:**
- Check internet connection
- Try running setup script again

**Training Doesn't Start:**
- Verify dataset format
- Check that Piper dependencies are installed

**Poor Quality Results:**
- Ensure your dataset has consistent audio quality
- Check that text transcriptions are accurate
- Consider training for more epochs

## Model Information

**Pretrained Model**: `speaches-ai/piper-ne_NP-google-medium`
- **Language**: Nepali (ne_NP)
- **Quality**: Medium
- **Speakers**: 18
- **Dataset**: Google
- **Size**: 76.8 MB
- **Format**: ONNX

This model was trained on high-quality Nepali speech data and provides an excellent starting point for fine-tuning on your specific voice and use case.
