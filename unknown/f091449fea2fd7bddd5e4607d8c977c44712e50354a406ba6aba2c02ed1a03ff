#!/usr/bin/env python3
"""
Complete workflow for fine-tuning with the pretrained Nepali model.
This script handles the entire process from setup to training.
"""

import subprocess
import sys
import os
from pathlib import Path
import json

def run_step(step_name, script_path, description):
    """Run a step in the fine-tuning workflow."""
    print(f"\n{'='*60}")
    print(f"🔄 STEP: {step_name}")
    print(f"📝 {description}")
    print(f"🚀 Running: {script_path}")
    print('='*60)
    
    try:
        result = subprocess.run([sys.executable, str(script_path)], check=True)
        print(f"✅ {step_name} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {step_name} failed with error: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ Script not found: {script_path}")
        return False

def check_dataset():
    """Check if dataset is prepared."""
    dataset_dir = Path("training_data")
    metadata_file = dataset_dir / "metadata.csv"
    wav_dir = dataset_dir / "wav"
    
    if not dataset_dir.exists() or not metadata_file.exists() or not wav_dir.exists():
        print("❌ Dataset not found!")
        print("Please make sure you have:")
        print("  - training_data/metadata.csv")
        print("  - training_data/wav/ (with audio files)")
        return False
    
    # Count audio files
    wav_files = list(wav_dir.glob("*.wav"))
    print(f"✅ Dataset found: {len(wav_files)} audio files")
    return True

def show_progress_info():
    """Show information about monitoring training progress."""
    print(f"\n{'='*60}")
    print("📊 MONITORING TRAINING PROGRESS")
    print('='*60)
    print("You can monitor the training progress using TensorBoard:")
    print("1. Open a new terminal")
    print("2. Navigate to the finetune directory")
    print("3. Run: tensorboard --logdir training_output/lightning_logs")
    print("4. Open http://localhost:6006 in your browser")
    print("\nTraining metrics to watch:")
    print("  - train_loss: Should decrease over time")
    print("  - val_loss: Should decrease and stay close to train_loss")
    print("  - learning_rate: Will change according to schedule")

def main():
    """Main workflow function."""
    print("🎯 COMPLETE PRETRAINED FINE-TUNING WORKFLOW")
    print("📍 Base model: speaches-ai/piper-ne_NP-google-medium")
    print("🎤 Target: Your custom Nepali voice")
    
    # Check if dataset exists
    if not check_dataset():
        print("\n💡 Please prepare your dataset first:")
        print("1. Place your audio files in training_data/wav/")
        print("2. Create training_data/metadata.csv with format: filename|text")
        sys.exit(1)
    
    # Step 1: Setup pretrained model
    step1_success = run_step(
        "1. Setup Pretrained Model",
        "setup_pretrained_finetuning.py",
        "Download and setup the pretrained Nepali model from Hugging Face"
    )
    
    if not step1_success:
        print("❌ Failed to setup pretrained model. Exiting.")
        sys.exit(1)
    
    # Step 2: Prepare dataset
    step2_success = run_step(
        "2. Prepare Dataset",
        "prepare_dataset_for_training.py",
        "Convert your dataset to Piper training format"
    )
    
    if not step2_success:
        print("❌ Failed to prepare dataset. Exiting.")
        sys.exit(1)
    
    # Show training info before starting
    show_progress_info()
    
    # Ask user if they want to continue
    print(f"\n{'='*60}")
    print("⚠️  READY TO START FINE-TUNING")
    print('='*60)
    print("The fine-tuning process will:")
    print("  - Use the pretrained Nepali model as starting point")
    print("  - Train on your custom dataset")
    print("  - Take several hours depending on your GPU")
    print("  - Save checkpoints every 25 epochs")
    
    response = input("\n🤔 Do you want to start fine-tuning now? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("⏸️  Fine-tuning postponed. You can run it later with:")
        print("   python train_with_pretrained.py")
        return
    
    # Step 3: Run fine-tuning
    step3_success = run_step(
        "3. Fine-tune Model",
        "train_with_pretrained.py",
        "Fine-tune the pretrained model on your dataset"
    )
    
    if step3_success:
        print(f"\n{'='*60}")
        print("🎉 FINE-TUNING WORKFLOW COMPLETED!")
        print('='*60)
        print("✅ Your custom Nepali TTS model has been trained!")
        print("\n📁 Output files:")
        print("  - training_output/lightning_logs/ (training logs)")
        print("  - training_output/lightning_logs/version_X/checkpoints/ (model checkpoints)")
        print("\n🔄 Next steps:")
        print("1. Export the best checkpoint to ONNX format")
        print("2. Test the model with your API")
        print("3. Deploy the model for production use")
        print("\n💡 The fine-tuned model should have:")
        print("  - Better pronunciation for your specific voice")
        print("  - Improved quality thanks to pretrained knowledge")
        print("  - Faster convergence compared to training from scratch")
        
    else:
        print(f"\n{'='*60}")
        print("❌ FINE-TUNING FAILED")
        print('='*60)
        print("Please check the error messages above and:")
        print("1. Ensure your GPU has enough memory")
        print("2. Check that your dataset is properly formatted")
        print("3. Verify all dependencies are installed")
        print("4. Try reducing batch size if you get memory errors")

if __name__ == "__main__":
    main()
