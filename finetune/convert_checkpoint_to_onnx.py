#!/usr/bin/env python3
"""
Convert PyTorch checkpoint to ONNX model for inference.
This script will be used after training to convert the checkpoint at epoch 10 or 20.
"""

import sys
import os
from pathlib import Path
import argparse

def find_latest_checkpoint():
    """Find the latest checkpoint in lightning_logs."""
    lightning_logs = Path("training_output/lightning_logs")
    
    if not lightning_logs.exists():
        print("❌ No lightning_logs directory found")
        return None
    
    # Find the latest version directory
    version_dirs = [d for d in lightning_logs.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        print("❌ No version directories found")
        return None
    
    latest_version = max(version_dirs, key=lambda x: int(x.name.split("_")[1]))
    checkpoints_dir = latest_version / "checkpoints"
    
    if not checkpoints_dir.exists():
        print(f"❌ No checkpoints directory found in {latest_version}")
        return None
    
    # Find checkpoint files
    checkpoint_files = list(checkpoints_dir.glob("*.ckpt"))
    if not checkpoint_files:
        print(f"❌ No checkpoint files found in {checkpoints_dir}")
        return None
    
    # Get the latest checkpoint (highest epoch)
    latest_checkpoint = max(checkpoint_files, key=lambda x: x.stat().st_mtime)
    
    print(f"✅ Found latest checkpoint: {latest_checkpoint}")
    return latest_checkpoint

def convert_checkpoint_to_onnx(checkpoint_path, output_dir="exported_model"):
    """Convert checkpoint to ONNX model."""
    
    # Add piper to Python path
    piper_path = Path("piper/src/python")
    sys.path.insert(0, str(piper_path))
    
    try:
        from piper_train.vits.export_onnx import export_onnx
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        print(f"🔄 Converting {checkpoint_path} to ONNX...")
        print(f"📁 Output directory: {output_path}")
        
        # Export to ONNX
        export_onnx(
            checkpoint_path=str(checkpoint_path),
            output_dir=str(output_path),
        )
        
        print(f"✅ ONNX export completed!")
        print(f"📁 Files created in: {output_path}")
        
        # List created files
        for file in output_path.iterdir():
            if file.is_file():
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"   - {file.name} ({size_mb:.1f} MB)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import piper_train: {e}")
        return False
    except Exception as e:
        print(f"❌ Export failed: {e}")
        return False

def test_onnx_model(model_dir="exported_model", test_text="नमस्कार, यो एक परीक्षण हो।"):
    """Test the exported ONNX model."""
    
    model_path = Path(model_dir) / "model.onnx"
    config_path = Path(model_dir) / "config.json"
    
    if not model_path.exists() or not config_path.exists():
        print(f"❌ Model files not found in {model_dir}")
        return False
    
    try:
        # Add piper to Python path
        piper_path = Path("piper/src/python")
        sys.path.insert(0, str(piper_path))
        
        from piper import PiperVoice
        import wave
        
        print(f"🎤 Testing ONNX model with text: '{test_text}'")
        
        # Load the voice
        voice = PiperVoice.load(str(model_path), config_path=str(config_path))
        
        # Generate audio
        audio_data = voice.synthesize(test_text)
        
        # Save test audio
        output_file = "test_output.wav"
        with wave.open(output_file, 'wb') as wav_file:
            wav_file.setparams((1, 2, voice.config.audio.sample_rate, 0, 'NONE', 'NONE'))
            wav_file.writeframes(audio_data)
        
        print(f"✅ Test audio generated: {output_file}")
        print(f"🎵 Sample rate: {voice.config.audio.sample_rate} Hz")
        print(f"📊 Audio length: {len(audio_data) / (voice.config.audio.sample_rate * 2):.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Convert checkpoint to ONNX and test")
    parser.add_argument("--checkpoint", help="Path to checkpoint file (auto-detect if not provided)")
    parser.add_argument("--output-dir", default="exported_model", help="Output directory for ONNX model")
    parser.add_argument("--test-text", default="नमस्कार, यो एक परीक्षण हो।", help="Text to test the model")
    parser.add_argument("--no-test", action="store_true", help="Skip testing the exported model")
    
    args = parser.parse_args()
    
    print("🔄 Checkpoint to ONNX Converter")
    print("=" * 40)
    
    # Find checkpoint
    if args.checkpoint:
        checkpoint_path = Path(args.checkpoint)
        if not checkpoint_path.exists():
            print(f"❌ Checkpoint file not found: {checkpoint_path}")
            sys.exit(1)
    else:
        checkpoint_path = find_latest_checkpoint()
        if not checkpoint_path:
            print("❌ No checkpoint found. Make sure training has created at least one checkpoint.")
            sys.exit(1)
    
    # Convert to ONNX
    success = convert_checkpoint_to_onnx(checkpoint_path, args.output_dir)
    if not success:
        sys.exit(1)
    
    # Test the model
    if not args.no_test:
        print("\n" + "=" * 40)
        print("🧪 Testing exported model...")
        test_success = test_onnx_model(args.output_dir, args.test_text)
        if test_success:
            print("\n🎉 Export and test completed successfully!")
        else:
            print("\n⚠️ Export completed but test failed")
    else:
        print("\n🎉 Export completed successfully!")

if __name__ == "__main__":
    main()
