#!/usr/bin/env python3
"""
Continue fine-tuning the Nepali TTS model with all available data.
This script resumes training from the latest checkpoint and trains for additional epochs.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_checkpoint():
    """Find the latest checkpoint from training."""
    lightning_logs = Path("finetune/training_output/lightning_logs")
    
    if not lightning_logs.exists():
        logger.error("No lightning_logs directory found")
        return None
    
    # Find the latest version directory
    version_dirs = [d for d in lightning_logs.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        logger.error("No version directories found")
        return None
    
    latest_version = max(version_dirs, key=lambda x: int(x.name.split("_")[1]))
    checkpoints_dir = latest_version / "checkpoints"
    
    if not checkpoints_dir.exists():
        logger.error(f"No checkpoints directory found in {latest_version}")
        return None
    
    # Find checkpoint files
    checkpoint_files = list(checkpoints_dir.glob("*.ckpt"))
    if not checkpoint_files:
        logger.error(f"No checkpoint files found in {checkpoints_dir}")
        return None
    
    # Get the latest checkpoint (highest epoch)
    latest_checkpoint = max(checkpoint_files, key=lambda x: x.stat().st_mtime)
    
    logger.info(f"Found latest checkpoint: {latest_checkpoint}")
    return latest_checkpoint

def check_gpu_availability():
    """Check if GPU is available for training."""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("GPU detected and available for training")
            return True
        else:
            logger.warning("nvidia-smi failed, using CPU training")
            return False
    except FileNotFoundError:
        logger.warning("nvidia-smi not found, using CPU training")
        return False

def continue_training(
    checkpoint_path,
    additional_epochs=50,
    batch_size=32,
    learning_rate=1e-4,
    use_gpu=True,
    validation_split=0.1
):
    """Continue training from checkpoint."""
    
    # Change to piper training directory
    piper_train_dir = Path("finetune/piper/src/python")
    if not piper_train_dir.exists():
        logger.error(f"Piper training directory not found: {piper_train_dir}")
        return False
    
    # Use virtual environment python - get absolute path from project root
    project_root = Path.cwd()
    venv_python = project_root / ".venv" / "bin" / "python"
    if not venv_python.exists():
        logger.error(f"Virtual environment python not found at {venv_python}")
        return False

    # Prepare training command - use absolute path for dataset
    dataset_dir = project_root / "finetune" / "training_data"
    cmd = [
        str(venv_python), "-m", "piper_train",
        "--dataset-dir", str(dataset_dir),
        "--checkpoint-epochs", "10",
        "--max_epochs", str(additional_epochs),
        "--batch-size", str(batch_size),
        "--validation-split", str(validation_split),
        "--learning-rate", str(learning_rate),
        "--resume_from_checkpoint", str(project_root / checkpoint_path),
        "--quality", "medium",
        "--seed", "1234"
    ]
    
    # Add GPU/CPU specific arguments
    if use_gpu and check_gpu_availability():
        cmd.extend([
            "--accelerator", "gpu",
            "--devices", "1",
            "--precision", "16-mixed"  # Use mixed precision for faster training
        ])
        logger.info("Using GPU acceleration with mixed precision")
    else:
        cmd.extend([
            "--accelerator", "cpu",
            "--devices", "1"
        ])
        logger.info("Using CPU training")
    
    logger.info(f"Starting continued training with command: {' '.join(cmd)}")
    logger.info(f"Additional epochs: {additional_epochs}")
    logger.info(f"Batch size: {batch_size}")
    logger.info(f"Learning rate: {learning_rate}")
    logger.info(f"Validation split: {validation_split}")
    
    try:
        # Run training
        process = subprocess.Popen(
            cmd,
            cwd=piper_train_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Stream output in real-time
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            logger.info("Training completed successfully!")
            return True
        else:
            logger.error(f"Training failed with return code: {process.returncode}")
            return False
            
    except Exception as e:
        logger.error(f"Training failed with exception: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Continue fine-tuning Nepali TTS model")
    parser.add_argument("--epochs", type=int, default=100, help="Additional epochs to train (default: 100)")
    parser.add_argument("--batch-size", type=int, default=32, help="Batch size (default: 32)")
    parser.add_argument("--learning-rate", type=float, default=1e-4, help="Learning rate (default: 1e-4)")
    parser.add_argument("--validation-split", type=float, default=0.1, help="Validation split (default: 0.1)")
    parser.add_argument("--cpu-only", action="store_true", help="Force CPU training")
    parser.add_argument("--checkpoint", help="Specific checkpoint path (auto-detect if not provided)")
    
    args = parser.parse_args()
    
    logger.info("🎯 Continuing Fine-tuning of Nepali TTS Model")
    logger.info("=" * 60)
    
    # Check if we're in the right directory
    if not Path("finetune").exists():
        logger.error("Please run this script from the project root directory")
        sys.exit(1)
    
    # Find checkpoint
    if args.checkpoint:
        checkpoint_path = Path(args.checkpoint)
        if not checkpoint_path.exists():
            logger.error(f"Checkpoint file not found: {checkpoint_path}")
            sys.exit(1)
    else:
        checkpoint_path = find_latest_checkpoint()
        if not checkpoint_path:
            logger.error("No checkpoint found. Please train a model first.")
            sys.exit(1)
    
    # Check training data
    training_data_dir = Path("finetune/training_data")
    if not training_data_dir.exists():
        logger.error("Training data directory not found")
        sys.exit(1)
    
    metadata_file = training_data_dir / "metadata.csv"
    if not metadata_file.exists():
        logger.error("Metadata file not found")
        sys.exit(1)
    
    # Count training samples
    with open(metadata_file, 'r', encoding='utf-8') as f:
        num_samples = sum(1 for line in f)
    
    logger.info(f"📊 Training Data Summary:")
    logger.info(f"   Total samples: {num_samples}")
    logger.info(f"   Training samples: {int(num_samples * (1 - args.validation_split))}")
    logger.info(f"   Validation samples: {int(num_samples * args.validation_split)}")
    logger.info(f"   Checkpoint: {checkpoint_path}")
    
    # Start training
    success = continue_training(
        checkpoint_path=checkpoint_path,
        additional_epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        use_gpu=not args.cpu_only,
        validation_split=args.validation_split
    )
    
    if success:
        logger.info("\n🎉 Training completed successfully!")
        logger.info("📁 Check finetune/training_output/lightning_logs/ for new checkpoints")
        logger.info("🔄 Convert the latest checkpoint to ONNX using:")
        logger.info("   python finetune/convert_checkpoint_to_onnx.py")
    else:
        logger.error("\n❌ Training failed. Check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
