# SSH Configuration for VSCode Remote Development
# This file contains SSH connection details for your T4 GPU server

Host t4-server
    HostName *************
    User kshitiz
    Port 22
    # Password authentication (you'll be prompted for password)
    PreferredAuthentications password,keyboard-interactive
    # Keep connection alive
    ServerAliveInterval 60
    ServerAliveCountMax 3
    # Forward ports for tensorboard and jupyter
    LocalForward 6006 localhost:6006
    LocalForward 8888 localhost:8888
    # Compression for faster file transfers
    Compression yes
    # Reuse connections
    ControlMaster auto
    ControlPath ~/.ssh/control-%r@%h:%p
    ControlPersist 10m

# Alternative host entry with different name
Host gpu-server
    HostName *************
    User kshitiz
    Port 22
    PreferredAuthentications password,keyboard-interactive
    ServerAliveInterval 60
    ServerAliveCountMax 3
    LocalForward 6006 localhost:6006
    LocalForward 8888 localhost:8888
    Compression yes
