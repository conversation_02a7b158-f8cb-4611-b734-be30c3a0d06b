# Piper TTS Complete Solution

A scalable Text-to-Speech solution with FastAPI backend and React frontend for Nepali language.

## Features

### Backend (FastAPI)
- 🚀 **Scalable Architecture**: Async processing with thread pool for concurrent requests
- 🎯 **Optimized ONNX Runtime**: Memory-efficient model loading with performance optimizations
- 🔊 **18 Nepali Speakers**: Multiple voice options with speaker selection
- ⚡ **Real-time Synthesis**: Fast audio generation with configurable parameters
- 🌐 **CORS Enabled**: Ready for frontend integration
- 📊 **Health Monitoring**: API status and voice information endpoints

### Frontend (React + Tailwind CSS)
- 🎨 **Modern UI**: Clean, responsive dashboard with Tailwind CSS 3
- 🎛️ **Advanced Controls**: Speech rate, noise controls, and speaker selection
- 🔄 **Real-time Status**: Live API connection monitoring
- 🎵 **Audio Playback**: Built-in audio player with download functionality
- 📱 **Mobile Responsive**: Works on all device sizes
- ⚡ **Quick Tests**: Pre-configured Nepali text samples

## Quick Start

### Option 1: Start Both Services
```bash
./start_services.sh
```

### Option 2: Manual Setup

1. **Install Dependencies**:
```bash
uv sync
```

2. **Start API Server**:
```bash
source .venv/bin/activate
python run_piper_api.py
```

3. **Start Frontend** (in new terminal):
```bash
cd piper_frontend
npm start
```

## Access Points

- 🌐 **Frontend Dashboard**: http://localhost:3000
- 📡 **API Server**: http://localhost:8000
- 📚 **API Documentation**: http://localhost:8000/docs

## Testing

```bash
python test_piper_tts.py
```

## API Endpoints

### Health Check
```
GET /
```

### Voice Information
```
GET /voice/info
```

### Available Speakers
```
GET /speakers
```

### Text-to-Speech Synthesis
```
GET /synthesize?text=नमस्ते&speaker_id=0&speech_rate=1.0
POST /synthesize
```

Parameters:
- `text`: Text to synthesize (required)
- `speaker_id`: Speaker ID (optional, 0-based)
- `speech_rate`: Speech rate (optional, 0.5-3.0, default: 1.0)
- `noise_scale`: Generator noise (optional, 0.0-1.0)
- `noise_w`: Phoneme width noise (optional, 0.0-1.0)
- `sentence_silence`: Silence after sentences (optional, 0.0-2.0 seconds)

## Models

The API automatically loads the Nepali model from:
- `models/ne_NP-google-medium.onnx`
- `models/ne_NP-google-medium.onnx.json`

## Dependencies

- FastAPI with uvicorn
- ONNX Runtime
- piper-phonemize
- NumPy
- Requests (for testing)