#!/usr/bin/env python3
"""
Test script for voice cloning capabilities in Piper TTS API.
"""

import requests
import json
import time
from pathlib import Path

API_BASE = "http://localhost:8000"

def test_api_health():
    """Test if the API is running."""
    try:
        response = requests.get(f"{API_BASE}/")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure it's running on localhost:8000")
        return False

def test_voice_cloning_info():
    """Test voice cloning info endpoint."""
    try:
        response = requests.get(f"{API_BASE}/voice-cloning/info")
        if response.status_code == 200:
            info = response.json()
            print("✅ Voice cloning info:")
            print(f"   Available models: {info['available_models']}")
            print(f"   Supported languages: {len(info['supported_languages'])} languages")
            print(f"   Max reference duration: {info['max_reference_duration']} seconds")
            return True
        else:
            print(f"❌ Voice cloning info failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Voice cloning info error: {e}")
        return False

def test_available_models():
    """Test available models endpoint."""
    try:
        response = requests.get(f"{API_BASE}/voice-cloning/models")
        if response.status_code == 200:
            models = response.json()
            print("✅ Available voice cloning models:")
            for model_id, model_info in models['available_models'].items():
                print(f"   - {model_info['name']} ({model_id})")
                print(f"     Capabilities: {model_info['capabilities']}")
                print(f"     Quality: {model_info['quality']}, Speed: {model_info['speed']}")
            return models['available_models']
        else:
            print(f"❌ Models list failed: {response.status_code}")
            return {}
    except Exception as e:
        print(f"❌ Models list error: {e}")
        return {}

def create_test_audio():
    """Create a simple test audio file using the regular TTS."""
    try:
        # Use regular Piper TTS to create reference audio
        response = requests.post(
            f"{API_BASE}/synthesize",
            json={
                "text": "Hello, this is a test voice for cloning. My name is Test Speaker.",
                "speaker_id": 0
            }
        )
        
        if response.status_code == 200:
            test_audio_path = Path("test_reference.wav")
            with open(test_audio_path, "wb") as f:
                f.write(response.content)
            print(f"✅ Created test reference audio: {test_audio_path}")
            return test_audio_path
        else:
            print(f"❌ Failed to create test audio: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Test audio creation error: {e}")
        return None

def test_voice_cloning(reference_audio_path, available_models):
    """Test voice cloning with available models."""
    if not reference_audio_path or not reference_audio_path.exists():
        print("❌ No reference audio available for testing")
        return False
    
    test_text = "This is a test of voice cloning. The voice should sound similar to the reference audio."
    
    for model_id in available_models.keys():
        print(f"\n🎤 Testing voice cloning with {model_id}...")
        
        try:
            with open(reference_audio_path, "rb") as audio_file:
                files = {
                    "reference_audio": ("reference.wav", audio_file, "audio/wav")
                }
                data = {
                    "text": test_text,
                    "model_type": model_id,
                    "language": "en",
                    "speed": 1.0,
                    "timbre_strength": 1.0
                }
                
                response = requests.post(
                    f"{API_BASE}/voice-cloning/clone",
                    files=files,
                    data=data,
                    timeout=60  # Voice cloning can take time
                )
                
                if response.status_code == 200:
                    output_path = Path(f"cloned_voice_{model_id}.wav")
                    with open(output_path, "wb") as f:
                        f.write(response.content)
                    print(f"   ✅ Voice cloning successful: {output_path}")
                else:
                    print(f"   ❌ Voice cloning failed: {response.status_code}")
                    if response.content:
                        try:
                            error = response.json()
                            print(f"      Error: {error.get('detail', 'Unknown error')}")
                        except:
                            print(f"      Error: {response.text[:200]}")
        
        except Exception as e:
            print(f"   ❌ Voice cloning error: {e}")

def test_hybrid_synthesis(reference_audio_path):
    """Test hybrid synthesis (Piper + Voice Cloning)."""
    if not reference_audio_path or not reference_audio_path.exists():
        print("❌ No reference audio available for hybrid testing")
        return False
    
    print(f"\n🔀 Testing hybrid synthesis...")
    
    try:
        with open(reference_audio_path, "rb") as audio_file:
            files = {
                "reference_audio": ("reference.wav", audio_file, "audio/wav")
            }
            data = {
                "text": "यो हाइब्रिड सिन्थेसिसको परीक्षण हो। यसले पाइपर र भ्वाइस क्लोनिङ दुवै प्रयोग गर्छ।",
                "base_model": "finetuned_custom",
                "cloning_model": "f5_tts",
                "cloning_strength": 0.7,
                "language": "ne",
                "speed": 1.0
            }
            
            response = requests.post(
                f"{API_BASE}/voice-cloning/hybrid-synthesis",
                files=files,
                data=data,
                timeout=60
            )
            
            if response.status_code == 200:
                output_path = Path("hybrid_synthesis.wav")
                with open(output_path, "wb") as f:
                    f.write(response.content)
                print(f"   ✅ Hybrid synthesis successful: {output_path}")
                return True
            else:
                print(f"   ❌ Hybrid synthesis failed: {response.status_code}")
                if response.content:
                    try:
                        error = response.json()
                        print(f"      Error: {error.get('detail', 'Unknown error')}")
                    except:
                        print(f"      Error: {response.text[:200]}")
                return False
    
    except Exception as e:
        print(f"   ❌ Hybrid synthesis error: {e}")
        return False

def main():
    """Main test function."""
    print("🎯 Testing Voice Cloning Capabilities")
    print("=" * 50)
    
    # Test API health
    if not test_api_health():
        print("\n💡 Start the API with: cd piper_api && python start_api.py")
        return
    
    # Test voice cloning info
    if not test_voice_cloning_info():
        return
    
    # Test available models
    available_models = test_available_models()
    if not available_models:
        print("\n⚠️  No voice cloning models available.")
        print("   Run: ./install_voice_cloning.sh to install models")
        return
    
    # Create test reference audio
    reference_audio = create_test_audio()
    
    # Test voice cloning
    if reference_audio:
        test_voice_cloning(reference_audio, available_models)
        test_hybrid_synthesis(reference_audio)
    
    print("\n🎉 Voice cloning tests completed!")
    print("\n📁 Generated files:")
    for file_path in Path(".").glob("*.wav"):
        if file_path.name.startswith(("test_", "cloned_", "hybrid_")):
            print(f"   - {file_path}")
    
    print("\n💡 Tips:")
    print("   - Use your own audio files for better voice cloning results")
    print("   - Reference audio should be 3-30 seconds long")
    print("   - Clear, high-quality audio works best")
    print("   - Try different models for different use cases")

if __name__ == "__main__":
    main()
