#!/usr/bin/env python3
"""
Simple startup script for the Piper TTS API.
This handles import issues and starts the FastAPI server.
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Now import and run the FastAPI app
if __name__ == "__main__":
    import uvicorn
    from fastapi_server import app
    
    print("🚀 Starting Piper TTS API...")
    print("📍 API will be available at: http://localhost:8000")
    print("📖 API docs will be available at: http://localhost:8000/docs")
    print("🛑 Press Ctrl+C to stop the server")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
