#!/usr/bin/env python3
"""
Dynamic model manager for Piper TTS API.
Automatically detects available models and provides model switching functionality.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """Information about a TTS model."""
    id: str
    name: str
    description: str
    version: str
    language: str
    type: str
    quality: str
    speakers: int
    sample_rate: int
    model_size_mb: float
    training_data: str
    recommended_for: str
    model_path: str
    config_path: str
    metadata_path: str
    training_epochs: Optional[int] = None

class ModelManager:
    """Manages available TTS models dynamically."""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.available_models: Dict[str, ModelInfo] = {}
        self.current_model_id: Optional[str] = None
        self.scan_models()
    
    def scan_models(self) -> None:
        """Scan the models directory for available models."""
        self.available_models.clear()
        
        if not self.models_dir.exists():
            logger.warning(f"Models directory not found: {self.models_dir}")
            return
        
        for model_dir in self.models_dir.iterdir():
            if not model_dir.is_dir():
                continue
                
            model_path = model_dir / "model.onnx"
            config_path = model_dir / "config.json"
            metadata_path = model_dir / "metadata.json"
            
            # Check if required files exist
            if not (model_path.exists() and config_path.exists()):
                logger.warning(f"Skipping {model_dir.name}: missing model.onnx or config.json")
                continue
            
            # Load metadata if available
            metadata = {}
            if metadata_path.exists():
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                except Exception as e:
                    logger.warning(f"Failed to load metadata for {model_dir.name}: {e}")
            
            # Create ModelInfo
            model_info = ModelInfo(
                id=model_dir.name,
                name=metadata.get('name', model_dir.name.replace('_', ' ').title()),
                description=metadata.get('description', f'TTS model: {model_dir.name}'),
                version=metadata.get('version', '1.0.0'),
                language=metadata.get('language', 'ne_NP'),
                type=metadata.get('type', 'unknown'),
                quality=metadata.get('quality', 'medium'),
                speakers=metadata.get('speakers', 1),
                sample_rate=metadata.get('sample_rate', 22050),
                model_size_mb=metadata.get('model_size_mb', model_path.stat().st_size / (1024 * 1024)),
                training_data=metadata.get('training_data', 'Unknown'),
                recommended_for=metadata.get('recommended_for', 'General use'),
                model_path=str(model_path),
                config_path=str(config_path),
                metadata_path=str(metadata_path),
                training_epochs=metadata.get('training_epochs')
            )
            
            self.available_models[model_dir.name] = model_info
            logger.info(f"Found model: {model_info.name} ({model_info.id})")
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models."""
        return [
            {
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "version": model.version,
                "language": model.language,
                "type": model.type,
                "quality": model.quality,
                "speakers": model.speakers,
                "sample_rate": model.sample_rate,
                "model_size_mb": round(model.model_size_mb, 1),
                "training_data": model.training_data,
                "recommended_for": model.recommended_for,
                "training_epochs": model.training_epochs,
                "is_current": model.id == self.current_model_id
            }
            for model in self.available_models.values()
        ]
    
    def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.available_models.get(model_id)
    
    def get_model_paths(self, model_id: str) -> Optional[tuple[str, str]]:
        """Get model and config paths for a specific model."""
        model_info = self.get_model_info(model_id)
        if model_info:
            return model_info.model_path, model_info.config_path
        return None
    
    def set_current_model(self, model_id: str) -> bool:
        """Set the current active model."""
        if model_id in self.available_models:
            self.current_model_id = model_id
            return True
        return False
    
    def get_current_model(self) -> Optional[ModelInfo]:
        """Get the current active model."""
        if self.current_model_id:
            return self.available_models.get(self.current_model_id)
        return None
    
    def get_default_model_id(self) -> Optional[str]:
        """Get the recommended default model ID."""
        # Priority order: finetuned > original_google > pretrained_checkpoint > any other
        priority_order = ['finetuned', 'original_google', 'pretrained_checkpoint']
        
        for model_id in priority_order:
            if model_id in self.available_models:
                return model_id
        
        # If none of the priority models exist, return the first available
        if self.available_models:
            return next(iter(self.available_models.keys()))
        
        return None
    
    def refresh_models(self) -> None:
        """Refresh the list of available models."""
        self.scan_models()
