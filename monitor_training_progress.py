#!/usr/bin/env python3
"""
Monitor training progress and provide real-time updates.
"""

import time
import os
from pathlib import Path
import json
import argparse

def get_latest_version_dir():
    """Get the latest version directory."""
    lightning_logs = Path("finetune/training_output/lightning_logs")
    if not lightning_logs.exists():
        return None
    
    version_dirs = [d for d in lightning_logs.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        return None
    
    return max(version_dirs, key=lambda x: int(x.name.split("_")[1]))

def get_checkpoint_info(version_dir):
    """Get information about checkpoints."""
    checkpoints_dir = version_dir / "checkpoints"
    if not checkpoints_dir.exists():
        return []
    
    checkpoints = []
    for ckpt_file in checkpoints_dir.glob("*.ckpt"):
        # Extract epoch and step from filename
        name = ckpt_file.stem
        if "epoch=" in name and "step=" in name:
            try:
                epoch_part = name.split("epoch=")[1].split("-")[0]
                step_part = name.split("step=")[1].split(".")[0]
                epoch = int(epoch_part)
                step = int(step_part)
                size_mb = ckpt_file.stat().st_size / (1024 * 1024)
                mod_time = ckpt_file.stat().st_mtime
                checkpoints.append({
                    'file': ckpt_file.name,
                    'epoch': epoch,
                    'step': step,
                    'size_mb': size_mb,
                    'modified': mod_time
                })
            except (ValueError, IndexError):
                continue
    
    return sorted(checkpoints, key=lambda x: x['epoch'])

def monitor_training(refresh_interval=30):
    """Monitor training progress."""
    print("🔍 Monitoring Training Progress")
    print("=" * 50)
    print(f"Refresh interval: {refresh_interval} seconds")
    print("Press Ctrl+C to stop monitoring")
    print()
    
    last_checkpoint_count = 0
    last_epoch = 0
    
    try:
        while True:
            # Clear screen (works on most terminals)
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print("🎯 Nepali TTS Fine-tuning Progress Monitor")
            print("=" * 60)
            print(f"Last updated: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # Get latest version directory
            version_dir = get_latest_version_dir()
            if not version_dir:
                print("❌ No training runs found")
                time.sleep(refresh_interval)
                continue
            
            print(f"📁 Current training run: {version_dir.name}")
            
            # Get checkpoint information
            checkpoints = get_checkpoint_info(version_dir)
            
            if not checkpoints:
                print("⏳ No checkpoints found yet - training may be starting...")
            else:
                latest_checkpoint = checkpoints[-1]
                
                print(f"📊 Training Progress:")
                print(f"   Latest epoch: {latest_checkpoint['epoch']}")
                print(f"   Latest step: {latest_checkpoint['step']}")
                print(f"   Total checkpoints: {len(checkpoints)}")
                print(f"   Latest checkpoint size: {latest_checkpoint['size_mb']:.1f} MB")
                print()
                
                # Show progress since last check
                if latest_checkpoint['epoch'] > last_epoch:
                    epochs_progress = latest_checkpoint['epoch'] - last_epoch
                    print(f"🚀 Progress since last check: +{epochs_progress} epochs")
                    last_epoch = latest_checkpoint['epoch']
                
                if len(checkpoints) > last_checkpoint_count:
                    new_checkpoints = len(checkpoints) - last_checkpoint_count
                    print(f"💾 New checkpoints: +{new_checkpoints}")
                    last_checkpoint_count = len(checkpoints)
                
                print()
                print("📈 Recent Checkpoints:")
                # Show last 5 checkpoints
                recent_checkpoints = checkpoints[-5:]
                for ckpt in recent_checkpoints:
                    mod_time = time.strftime('%H:%M:%S', time.localtime(ckpt['modified']))
                    print(f"   Epoch {ckpt['epoch']:4d} | Step {ckpt['step']:6d} | {mod_time} | {ckpt['size_mb']:.1f}MB")
            
            # Check if training is still running
            print()
            print("🔍 Training Status:")
            
            # Check for recent activity (file modified in last 5 minutes)
            if checkpoints:
                latest_mod_time = max(ckpt['modified'] for ckpt in checkpoints)
                time_since_update = time.time() - latest_mod_time
                
                if time_since_update < 300:  # 5 minutes
                    print("   ✅ Training appears to be active")
                    print(f"   ⏱️  Last checkpoint: {time_since_update/60:.1f} minutes ago")
                else:
                    print("   ⚠️  Training may have stopped")
                    print(f"   ⏱️  Last checkpoint: {time_since_update/60:.1f} minutes ago")
            else:
                print("   ⏳ Waiting for training to start...")
            
            # Training data info
            metadata_file = Path("finetune/training_data/metadata.csv")
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    total_samples = sum(1 for line in f)
                print(f"   📊 Total training samples: {total_samples}")
            
            print()
            print(f"🔄 Next update in {refresh_interval} seconds...")
            print("   Press Ctrl+C to stop monitoring")
            
            time.sleep(refresh_interval)
            
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped by user")

def show_training_summary():
    """Show a summary of all training runs."""
    print("📊 Training Summary")
    print("=" * 40)
    
    lightning_logs = Path("finetune/training_output/lightning_logs")
    if not lightning_logs.exists():
        print("❌ No training logs found")
        return
    
    version_dirs = [d for d in lightning_logs.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        print("❌ No training runs found")
        return
    
    version_dirs.sort(key=lambda x: int(x.name.split("_")[1]))
    
    for version_dir in version_dirs:
        checkpoints = get_checkpoint_info(version_dir)
        if checkpoints:
            latest = checkpoints[-1]
            print(f"{version_dir.name}: Epoch {latest['epoch']}, Step {latest['step']}, {len(checkpoints)} checkpoints")
        else:
            print(f"{version_dir.name}: No checkpoints")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Monitor TTS training progress")
    parser.add_argument("--interval", type=int, default=30, help="Refresh interval in seconds (default: 30)")
    parser.add_argument("--summary", action="store_true", help="Show training summary and exit")
    
    args = parser.parse_args()
    
    if args.summary:
        show_training_summary()
    else:
        monitor_training(args.interval)

if __name__ == "__main__":
    main()
