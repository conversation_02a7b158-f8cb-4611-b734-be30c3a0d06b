#!/usr/bin/env python3
"""
Monitor the fine-tuning progress and show status updates.
"""

import time
import os
from pathlib import Path
import json

def check_training_progress():
    """Check the current training progress."""
    
    # Check for lightning logs
    lightning_logs = Path("training_output/lightning_logs")
    
    if not lightning_logs.exists():
        print("⏳ Training not started yet - no lightning_logs directory")
        return False
    
    # Find the latest version
    version_dirs = [d for d in lightning_logs.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        print("⏳ Training initializing - no version directories yet")
        return False
    
    latest_version = max(version_dirs, key=lambda x: int(x.name.split("_")[1]))
    print(f"📁 Training directory: {latest_version}")
    
    # Check for checkpoints
    checkpoints_dir = latest_version / "checkpoints"
    if checkpoints_dir.exists():
        checkpoint_files = list(checkpoints_dir.glob("*.ckpt"))
        if checkpoint_files:
            print(f"✅ Found {len(checkpoint_files)} checkpoint(s):")
            for ckpt in sorted(checkpoint_files):
                size_mb = ckpt.stat().st_size / (1024 * 1024)
                print(f"   - {ckpt.name} ({size_mb:.1f} MB)")
    
    # Check for metrics
    metrics_file = latest_version / "metrics.csv"
    if metrics_file.exists():
        print(f"📊 Metrics file found: {metrics_file}")
        # Read last few lines to show progress
        try:
            with open(metrics_file, 'r') as f:
                lines = f.readlines()
                if len(lines) > 1:
                    print(f"📈 Training progress: {len(lines)-1} steps logged")
                    if len(lines) > 5:
                        print("   Last few entries:")
                        for line in lines[-3:]:
                            print(f"   {line.strip()}")
        except Exception as e:
            print(f"⚠️ Could not read metrics: {e}")
    
    # Check for events (tensorboard logs)
    events_files = list(latest_version.glob("events.out.tfevents.*"))
    if events_files:
        print(f"📊 TensorBoard logs: {len(events_files)} file(s)")
        latest_event = max(events_files, key=lambda x: x.stat().st_mtime)
        size_kb = latest_event.stat().st_size / 1024
        print(f"   Latest: {latest_event.name} ({size_kb:.1f} KB)")
    
    return True

def check_gpu_usage():
    """Check GPU usage."""
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                mem_used, mem_total, gpu_util = line.split(', ')
                print(f"🖥️ GPU {i}: {mem_used}MB/{mem_total}MB ({gpu_util}% util)")
        else:
            print("⚠️ Could not get GPU info")
    except Exception as e:
        print(f"⚠️ GPU monitoring error: {e}")

def main():
    """Main monitoring function."""
    print("🔍 Fine-tuning Progress Monitor")
    print("=" * 50)
    
    print("\n📊 Training Status:")
    training_active = check_training_progress()
    
    print("\n🖥️ GPU Status:")
    check_gpu_usage()
    
    if training_active:
        print("\n💡 Tips:")
        print("   - Monitor with: tensorboard --logdir training_output/lightning_logs")
        print("   - Check logs: tail -f training_output/lightning_logs/version_*/events.out.tfevents.*")
        print("   - Checkpoints saved every 10 epochs")
        print("   - Training: epoch 2829 → 2849 (20 additional epochs)")
    else:
        print("\n⏳ Training is still initializing...")
        print("   This can take 2-5 minutes for large models")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
