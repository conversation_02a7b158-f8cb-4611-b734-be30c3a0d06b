accelerator: gpu
batch_size: 8
betas: !!python/tuple
- 0.8
- 0.99
c_kl: 1.0
c_mel: 45
channels: 1
checkpoint_epochs: 10
dataset:
- !!python/object/apply:pathlib.PosixPath
  - /
  - home
  - diwas
  - Documents
  - personal
  - test
  - finetune
  - training_output
  - dataset.jsonl
dataset_dir: &id001 !!python/object/apply:pathlib.PosixPath
- /
- home
- diwas
- Documents
- personal
- test
- finetune
- training_output
default_root_dir: *id001
devices: 1
early_stop_patience: 0
eps: 1.0e-09
filter_channels: 768
filter_length: 1024
gin_channels: 0
grad_clip: 1.0
hidden_channels: 192
hop_length: 256
init_lr_ratio: 1.0
inter_channels: 192
kernel_size: 3
learning_rate: 0.0001
log_every_n_steps: null
lr_decay: 0.999875
lr_reduce_enabled: false
lr_reduce_factor: 0.5
lr_reduce_patience: 10
max_epochs: 20
max_phoneme_ids: 300
mel_channels: 80
mel_fmax: null
mel_fmin: 0.0
monitor: val_loss
monitor_mode: min
n_heads: 2
n_layers: 6
n_layers_q: 3
num_ckpt: 1
num_speakers: 19
num_symbols: 256
num_test_examples: 50
num_workers: 1
override_learning_rate: false
p_dropout: 0.1
plot_save_path: null
precision: 16-mixed
quality: medium
random_seed: false
resblock: '2'
resblock_dilation_sizes: !!python/tuple
- !!python/tuple
  - 1
  - 2
- !!python/tuple
  - 2
  - 6
- !!python/tuple
  - 3
  - 12
resblock_kernel_sizes: !!python/tuple
- 3
- 5
- 7
resume_from_checkpoint: null
resume_from_single_speaker_checkpoint: null
sample_bytes: 2
sample_rate: 22050
save_last: null
seed: 1234
segment_size: 8192
show_plot: false
upsample_initial_channel: 256
upsample_kernel_sizes: !!python/tuple
- 16
- 16
- 8
upsample_rates: !!python/tuple
- 8
- 8
- 4
use_sdp: true
use_spectral_norm: false
validation_split: 0.1
warmup_epochs: 0
weight_decay: 0.001
win_length: 1024
