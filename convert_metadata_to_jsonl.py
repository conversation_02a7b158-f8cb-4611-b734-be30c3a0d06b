#!/usr/bin/env python3
"""
Convert metadata.csv to dataset.jsonl format for piper training.
"""

import csv
import json
import sys
from pathlib import Path

def convert_metadata_to_jsonl(metadata_path, output_path):
    """Convert metadata.csv to dataset.jsonl format."""
    
    metadata_file = Path(metadata_path)
    output_file = Path(output_path)
    
    if not metadata_file.exists():
        print(f"Error: Metadata file not found: {metadata_file}")
        return False
    
    print(f"Converting {metadata_file} to {output_file}")
    
    converted_count = 0
    
    with open(metadata_file, 'r', encoding='utf-8') as csv_file, \
         open(output_file, 'w', encoding='utf-8') as jsonl_file:
        
        csv_reader = csv.reader(csv_file, delimiter='|')
        
        for row in csv_reader:
            if len(row) >= 2:
                audio_path = row[0].strip()
                text = row[1].strip()
                
                # Create JSONL entry
                entry = {
                    "audio_path": audio_path,
                    "text": text,
                    "speaker_id": 0  # Single speaker
                }
                
                jsonl_file.write(json.dumps(entry, ensure_ascii=False) + '\n')
                converted_count += 1
    
    print(f"Converted {converted_count} entries to JSONL format")
    return True

def main():
    """Main function."""
    metadata_path = "finetune/training_data/metadata.csv"
    output_path = "finetune/training_data/dataset.jsonl"
    
    if convert_metadata_to_jsonl(metadata_path, output_path):
        print("✅ Conversion completed successfully!")
    else:
        print("❌ Conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
