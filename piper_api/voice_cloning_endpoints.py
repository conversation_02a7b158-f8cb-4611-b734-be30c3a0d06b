#!/usr/bin/env python3
"""
Voice cloning and timbre transfer endpoints for Piper TTS API.
"""

import io
import logging
from typing import Optional, List
from fastapi import Fast<PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.responses import Response
from pydantic import BaseModel, Field

try:
    from .voice_cloning_manager import (
        VoiceCloningManager, VoiceCloneRequest, TimbreTransferRequest,
        VoiceCloningModel
    )
except ImportError:
    from voice_cloning_manager import (
        VoiceCloningManager, VoiceCloneRequest, TimbreTransferRequest,
        VoiceCloningModel
    )

logger = logging.getLogger(__name__)

# Pydantic models for voice cloning requests
class VoiceCloneTextRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize with cloned voice", min_length=1, max_length=2000)
    model_type: str = Field("f5_tts", description="Voice cloning model to use")
    language: str = Field("en", description="Language code")
    speed: float = Field(1.0, description="Speech speed", gt=0.1, le=3.0)
    timbre_strength: float = Field(1.0, description="Strength of voice cloning", ge=0.1, le=2.0)
    emotion: Optional[str] = Field(None, description="Emotion to apply (if supported)")

class TimbreTransferFormRequest(BaseModel):
    model_type: str = Field("openvoice_v2", description="Model for timbre transfer")
    strength: float = Field(1.0, description="Transfer strength", ge=0.1, le=2.0)
    preserve_prosody: bool = Field(True, description="Preserve original prosody")

class VoiceCloningInfo(BaseModel):
    available_models: List[str]
    supported_languages: List[str]
    max_reference_duration: int  # seconds
    max_text_length: int

def add_voice_cloning_endpoints(app: FastAPI, voice_cloning_manager: VoiceCloningManager):
    """Add voice cloning endpoints to the FastAPI app."""
    
    @app.get("/voice-cloning/info", response_model=VoiceCloningInfo, summary="Voice cloning information")
    async def get_voice_cloning_info():
        """Get information about available voice cloning capabilities."""
        return VoiceCloningInfo(
            available_models=voice_cloning_manager.get_available_models(),
            supported_languages=["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hu", "ko", "ne"],
            max_reference_duration=30,  # seconds
            max_text_length=2000
        )
    
    @app.post("/voice-cloning/clone", summary="Clone voice from reference audio")
    async def clone_voice(
        text: str = Form(..., description="Text to synthesize"),
        reference_audio: UploadFile = File(..., description="Reference audio file (WAV/MP3)"),
        model_type: str = Form("f5_tts", description="Voice cloning model"),
        language: str = Form("en", description="Language code"),
        speed: float = Form(1.0, description="Speech speed"),
        timbre_strength: float = Form(1.0, description="Voice cloning strength"),
        emotion: Optional[str] = Form(None, description="Emotion (if supported)")
    ):
        """
        Clone a voice from reference audio and synthesize text.
        
        - **text**: Text to synthesize (max 2000 characters)
        - **reference_audio**: Audio file containing the voice to clone (3-30 seconds recommended)
        - **model_type**: Voice cloning model (f5_tts, xtts_v2, openvoice_v2)
        - **language**: Language code for synthesis
        - **speed**: Speech speed multiplier
        - **timbre_strength**: How strongly to apply the cloned voice characteristics
        - **emotion**: Emotion to apply (model-dependent)
        """
        try:
            # Validate model type
            try:
                model_enum = VoiceCloningModel(model_type)
            except ValueError:
                available_models = voice_cloning_manager.get_available_models()
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid model_type. Available models: {available_models}"
                )
            
            # Validate file type
            if not reference_audio.content_type.startswith(('audio/', 'video/')):
                raise HTTPException(
                    status_code=400,
                    detail="Reference audio must be an audio file"
                )
            
            # Read reference audio
            reference_audio_bytes = await reference_audio.read()
            
            if len(reference_audio_bytes) == 0:
                raise HTTPException(
                    status_code=400,
                    detail="Reference audio file is empty"
                )
            
            # Create voice clone request
            clone_request = VoiceCloneRequest(
                text=text,
                reference_audio=reference_audio_bytes,
                model_type=model_enum,
                language=language,
                speed=speed,
                emotion=emotion,
                timbre_strength=timbre_strength
            )
            
            # Perform voice cloning
            cloned_audio = await voice_cloning_manager.clone_voice_async(clone_request)
            
            return Response(
                content=cloned_audio,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=cloned_voice_{model_type}.wav"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Voice cloning failed: {e}")
            raise HTTPException(status_code=500, detail=f"Voice cloning failed: {str(e)}")
    
    @app.post("/voice-cloning/timbre-transfer", summary="Transfer timbre between audio samples")
    async def transfer_timbre(
        source_audio: UploadFile = File(..., description="Source audio to modify"),
        target_voice: UploadFile = File(..., description="Target voice reference"),
        model_type: str = Form("openvoice_v2", description="Timbre transfer model"),
        strength: float = Form(1.0, description="Transfer strength"),
        preserve_prosody: bool = Form(True, description="Preserve original prosody")
    ):
        """
        Transfer timbre from target voice to source audio.
        
        - **source_audio**: Audio file whose timbre will be modified
        - **target_voice**: Reference audio containing the desired timbre
        - **model_type**: Model to use for timbre transfer
        - **strength**: How strongly to apply the timbre transfer
        - **preserve_prosody**: Whether to preserve the original prosody/rhythm
        """
        try:
            # Validate model type
            try:
                model_enum = VoiceCloningModel(model_type)
            except ValueError:
                available_models = voice_cloning_manager.get_available_models()
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid model_type. Available models: {available_models}"
                )
            
            # Validate file types
            for file, name in [(source_audio, "source_audio"), (target_voice, "target_voice")]:
                if not file.content_type.startswith(('audio/', 'video/')):
                    raise HTTPException(
                        status_code=400,
                        detail=f"{name} must be an audio file"
                    )
            
            # Read audio files
            source_audio_bytes = await source_audio.read()
            target_voice_bytes = await target_voice.read()
            
            if len(source_audio_bytes) == 0 or len(target_voice_bytes) == 0:
                raise HTTPException(
                    status_code=400,
                    detail="Audio files cannot be empty"
                )
            
            # Create timbre transfer request
            transfer_request = TimbreTransferRequest(
                source_audio=source_audio_bytes,
                target_voice=target_voice_bytes,
                model_type=model_enum,
                strength=strength,
                preserve_prosody=preserve_prosody
            )
            
            # Perform timbre transfer
            transferred_audio = await voice_cloning_manager.transfer_timbre_async(transfer_request)
            
            return Response(
                content=transferred_audio,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=timbre_transfer_{model_type}.wav"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Timbre transfer failed: {e}")
            raise HTTPException(status_code=500, detail=f"Timbre transfer failed: {str(e)}")
    
    @app.post("/voice-cloning/hybrid-synthesis", summary="Hybrid synthesis with Piper + Voice Cloning")
    async def hybrid_synthesis(
        text: str = Form(..., description="Text to synthesize"),
        reference_audio: UploadFile = File(..., description="Reference audio for voice cloning"),
        base_model: str = Form("finetuned_custom", description="Base Piper model to use"),
        cloning_model: str = Form("f5_tts", description="Voice cloning model"),
        cloning_strength: float = Form(0.7, description="How much to apply voice cloning"),
        language: str = Form("ne", description="Language code"),
        speed: float = Form(1.0, description="Speech speed")
    ):
        """
        Hybrid synthesis: Use Piper for base TTS, then apply voice cloning.
        
        This combines the quality of your fine-tuned Piper model with voice cloning capabilities.
        """
        try:
            # This would integrate with your existing voice_manager
            # For now, we'll just do voice cloning
            
            # Validate cloning model
            try:
                cloning_model_enum = VoiceCloningModel(cloning_model)
            except ValueError:
                available_models = voice_cloning_manager.get_available_models()
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid cloning_model. Available models: {available_models}"
                )
            
            # Read reference audio
            reference_audio_bytes = await reference_audio.read()
            
            # Create voice clone request
            clone_request = VoiceCloneRequest(
                text=text,
                reference_audio=reference_audio_bytes,
                model_type=cloning_model_enum,
                language=language,
                speed=speed,
                timbre_strength=cloning_strength
            )
            
            # For now, just do voice cloning
            # TODO: Integrate with Piper base synthesis
            cloned_audio = await voice_cloning_manager.clone_voice_async(clone_request)
            
            return Response(
                content=cloned_audio,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=hybrid_synthesis.wav"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Hybrid synthesis failed: {e}")
            raise HTTPException(status_code=500, detail=f"Hybrid synthesis failed: {str(e)}")
    
    @app.get("/voice-cloning/models", summary="Get available voice cloning models")
    async def get_voice_cloning_models():
        """Get list of available voice cloning models and their capabilities."""
        models_info = {}
        
        for model_type in voice_cloning_manager.get_available_models():
            if model_type == "f5_tts":
                models_info[model_type] = {
                    "name": "F5-TTS",
                    "description": "High-quality zero-shot voice cloning",
                    "capabilities": ["voice_cloning"],
                    "languages": ["multilingual"],
                    "speed": "fast",
                    "quality": "high"
                }
            elif model_type == "xtts_v2":
                models_info[model_type] = {
                    "name": "XTTS v2",
                    "description": "Multilingual voice cloning by Coqui",
                    "capabilities": ["voice_cloning", "multilingual"],
                    "languages": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hu", "ko"],
                    "speed": "medium",
                    "quality": "high"
                }
            elif model_type == "openvoice_v2":
                models_info[model_type] = {
                    "name": "OpenVoice v2",
                    "description": "Instant voice cloning and timbre transfer",
                    "capabilities": ["voice_cloning", "timbre_transfer"],
                    "languages": ["multilingual"],
                    "speed": "fast",
                    "quality": "medium-high"
                }
        
        return {
            "available_models": models_info,
            "total_models": len(models_info)
        }

# Global voice cloning manager instance
voice_cloning_manager = VoiceCloningManager()
