#!/usr/bin/env python3
"""
Setup script for fine-tuning with the pretrained Nepali model from Hugging Face.
This script downloads the speaches-ai/piper-ne_NP-google-medium model and prepares it for fine-tuning.
"""

import subprocess
import sys
import os
from pathlib import Path
import json
import requests
from urllib.parse import urlparse

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        
        # Check if CUDA is available
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️ CUDA not available, will use CPU")
            
    except ImportError:
        print("❌ PyTorch not found. Please install PyTorch first.")
        return False
        
    try:
        import requests
        print("✅ Requests library available")
    except ImportError:
        print("❌ Requests library not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "requests"], check=True)
        
    return True

def download_file(url, destination):
    """Download a file from URL to destination."""
    print(f"📥 Downloading {url}...")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(destination, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\r📥 Progress: {percent:.1f}%", end="", flush=True)
        
        print(f"\n✅ Downloaded: {destination}")
        return True
    except Exception as e:
        print(f"\n❌ Failed to download {url}: {e}")
        return False

def download_pretrained_model():
    """Download the pretrained Nepali model from Hugging Face."""
    pretrained_dir = Path("pretrained_model")
    pretrained_dir.mkdir(exist_ok=True)
    
    # URLs for the pretrained model
    model_url = "https://huggingface.co/speaches-ai/piper-ne_NP-google-medium/resolve/main/model.onnx"
    config_url = "https://huggingface.co/speaches-ai/piper-ne_NP-google-medium/resolve/main/config.json"
    
    # Download paths
    model_path = pretrained_dir / "model.onnx"
    config_path = pretrained_dir / "config.json"
    
    # Download model if not exists
    if not model_path.exists():
        print("📦 Downloading pretrained ONNX model (76.8 MB)...")
        if not download_file(model_url, model_path):
            return False
    else:
        print(f"✅ Pretrained model already exists: {model_path}")
    
    # Download config if not exists
    if not config_path.exists():
        if not download_file(config_url, config_path):
            return False
    else:
        print(f"✅ Pretrained config already exists: {config_path}")
    
    return True

def setup_piper_training():
    """Clone and setup Piper training environment."""
    piper_dir = Path("piper")
    
    if not piper_dir.exists():
        print("📥 Cloning Piper repository...")
        try:
            subprocess.run([
                "git", "clone", 
                "https://github.com/rhasspy/piper.git"
            ], check=True)
            print("✅ Piper repository cloned successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to clone Piper repository")
            return False
    else:
        print("✅ Piper repository already exists")
    
    # Install Piper training dependencies
    print("📦 Installing Piper training dependencies...")
    try:
        subprocess.run([
            "pip", "install", "-e", "./piper/src/python"
        ], check=True)
        print("✅ Piper training dependencies installed")
    except subprocess.CalledProcessError:
        print("❌ Failed to install Piper training dependencies")
        return False
    
    return True

def create_training_config():
    """Create training configuration file based on pretrained model."""
    # Load pretrained config
    pretrained_config_path = Path("pretrained_model/config.json")
    
    if not pretrained_config_path.exists():
        print("❌ Pretrained config not found. Please download it first.")
        return False
    
    with open(pretrained_config_path, 'r', encoding='utf-8') as f:
        pretrained_config = json.load(f)
    
    print(f"📋 Loaded pretrained config:")
    print(f"   - Language: {pretrained_config['language']['name_english']} ({pretrained_config['language']['code']})")
    print(f"   - Speakers: {pretrained_config['num_speakers']}")
    print(f"   - Phonemes: {pretrained_config['num_symbols']}")
    print(f"   - Dataset: {pretrained_config['dataset']}")
    
    # Create fine-tuning config based on pretrained model
    config = {
        "dataset": "finetune",
        "audio": pretrained_config["audio"],  # Use same audio settings
        "espeak": pretrained_config["espeak"],  # Use same espeak settings
        "language": pretrained_config["language"],  # Use same language settings
        "inference": pretrained_config["inference"],  # Use same inference settings
        "phoneme_type": pretrained_config["phoneme_type"],
        "phoneme_map": pretrained_config["phoneme_map"],
        "phoneme_id_map": pretrained_config["phoneme_id_map"],  # Use same phoneme mapping
        "num_symbols": pretrained_config["num_symbols"],  # Use same symbol count
        "num_speakers": 1,  # Single speaker for fine-tuning
        "speaker_id_map": {},  # Will be populated during training
        "piper_version": pretrained_config["piper_version"]
    }
    
    # Create training_output directory
    training_dir = Path("training_output")
    training_dir.mkdir(exist_ok=True)
    
    # Save config
    config_path = training_dir / "config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=4, ensure_ascii=False)
    
    print(f"✅ Training config created: {config_path}")
    print(f"📋 Using phoneme mapping from pretrained model ({len(pretrained_config['phoneme_id_map'])} phonemes)")
    return True

def convert_onnx_to_checkpoint():
    """Convert ONNX model to PyTorch checkpoint for fine-tuning."""
    print("🔄 Converting ONNX model to PyTorch checkpoint...")
    
    # This is a placeholder - the actual conversion would need to be implemented
    # based on Piper's training code structure
    print("⚠️ ONNX to checkpoint conversion not yet implemented.")
    print("   You may need to find a PyTorch checkpoint version of the model")
    print("   or implement the conversion using Piper's model loading code.")
    
    return True

def main():
    """Main setup function."""
    print("🚀 Setting up Piper TTS fine-tuning with pretrained Nepali model...")
    print("📍 Model: speaches-ai/piper-ne_NP-google-medium")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Download pretrained model
    if not download_pretrained_model():
        sys.exit(1)
    
    # Setup Piper training
    if not setup_piper_training():
        sys.exit(1)
    
    # Create training config
    if not create_training_config():
        sys.exit(1)
    
    # Convert ONNX to checkpoint (if needed)
    convert_onnx_to_checkpoint()
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Prepare your dataset using prepare_dataset_for_training.py")
    print("2. Run fine-tuning using the updated train_nepali_model.py")
    print(f"\n📁 Pretrained model location: pretrained_model/")
    print(f"📁 Training config location: training_output/config.json")
    print("\n💡 The pretrained model has 18 speakers and high-quality Nepali phoneme mapping!")

if __name__ == "__main__":
    main()
