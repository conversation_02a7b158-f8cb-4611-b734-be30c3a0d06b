#!/usr/bin/env python3
"""
Fix metadata.csv to include speaker IDs for multi-speaker training.
"""

import csv
import json
from pathlib import Path

def extract_speaker_id(filename):
    """Extract speaker ID from filename like nep_0258_1234567890.wav -> 0258"""
    parts = filename.split('_')
    if len(parts) >= 2:
        return parts[1]  # Return the speaker ID part
    return "0000"  # Default speaker ID

def fix_metadata():
    """Fix metadata.csv to include speaker IDs."""
    
    metadata_file = Path("finetune/training_data/metadata.csv")
    output_file = Path("finetune/training_data/metadata_fixed.csv")
    
    # Load the speaker ID map from config
    config_file = Path("finetune/training_data/config.json")
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    speaker_id_map = config.get('speaker_id_map', {})
    print(f"Found {len(speaker_id_map)} speakers in config:")
    for speaker, id_num in speaker_id_map.items():
        print(f"  {speaker} -> {id_num}")
    
    converted_count = 0
    missing_speakers = set()
    
    with open(metadata_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        csv_reader = csv.reader(infile, delimiter='|')
        csv_writer = csv.writer(outfile, delimiter='|')
        
        for row in csv_reader:
            if len(row) >= 2:
                filename = row[0].strip()
                text = row[1].strip()
                
                # Extract speaker ID from filename
                speaker_id = extract_speaker_id(filename)
                
                # Get numeric speaker ID from map
                if speaker_id in speaker_id_map:
                    numeric_speaker_id = speaker_id_map[speaker_id]
                else:
                    # Use speaker ID 0 as default and track missing
                    numeric_speaker_id = 0
                    missing_speakers.add(speaker_id)
                
                # Write in format: filename|text|speaker_id
                csv_writer.writerow([filename, text, numeric_speaker_id])
                converted_count += 1
    
    print(f"\nConverted {converted_count} entries")
    
    if missing_speakers:
        print(f"Warning: Found {len(missing_speakers)} unknown speakers (assigned to speaker 0):")
        for speaker in sorted(missing_speakers):
            print(f"  {speaker}")
    
    # Replace original file
    output_file.replace(metadata_file)
    print(f"✅ Updated {metadata_file}")

def main():
    """Main function."""
    print("🔧 Fixing metadata.csv for multi-speaker training")
    print("=" * 50)
    
    fix_metadata()
    
    print("\n✅ Metadata fixed successfully!")
    print("Now you can reprocess the data with multi-speaker support.")

if __name__ == "__main__":
    main()
