#!/usr/bin/env python3
"""
TTS Application Startup Script
Starts both the Piper API backend and React frontend
"""

import os
import sys
import time
import signal
import subprocess
import requests
import json
from pathlib import Path

class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_status(message):
    print(f"{Colors.BLUE}[TTS-APP]{Colors.NC} {message}")

def print_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

def print_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

def print_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

class TTSAppManager:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.script_dir = Path(__file__).parent.absolute()
        
    def cleanup(self, signum=None, frame=None):
        """Cleanup processes on exit"""
        print_status("Shutting down TTS application...")
        
        if self.backend_process:
            print_status(f"Stopping backend (PID: {self.backend_process.pid})...")
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
            except:
                pass
                
        if self.frontend_process:
            print_status(f"Stopping frontend (PID: {self.frontend_process.pid})...")
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
            except:
                pass
                
        print_success("TTS application stopped")
        sys.exit(0)
        
    def check_requirements(self):
        """Check if all requirements are met"""
        os.chdir(self.script_dir)
        
        # Check virtual environment
        venv_path = self.script_dir / ".venv"
        if not venv_path.exists():
            print_error("Virtual environment not found. Please run setup first.")
            return False
            
        # Check frontend dependencies
        node_modules = self.script_dir / "piper_frontend" / "node_modules"
        if not node_modules.exists():
            print_warning("Frontend dependencies not found. Installing...")
            try:
                subprocess.run(["npm", "install"], 
                             cwd=self.script_dir / "piper_frontend", 
                             check=True)
            except subprocess.CalledProcessError:
                print_error("Failed to install frontend dependencies")
                return False
                
        # Check models directory
        models_dir = self.script_dir / "piper_api" / "models"
        if not models_dir.exists():
            print_error("Models directory not found. Please set up models first.")
            return False
            
        # Count available models
        model_files = list(models_dir.glob("*/model.onnx"))
        if not model_files:
            print_error("No models found in piper_api/models/. Please add models first.")
            return False
            
        print_success(f"Found {len(model_files)} TTS models")
        return True
        
    def start_backend(self):
        """Start the Piper TTS API backend"""
        print_status("Starting Piper TTS API backend...")
        
        # Determine Python executable path
        if os.name == 'nt':  # Windows
            python_exe = self.script_dir / ".venv" / "Scripts" / "python.exe"
        else:  # Unix/Linux/macOS
            python_exe = self.script_dir / ".venv" / "bin" / "python"
            
        cmd = [
            str(python_exe), "-m", "uvicorn", 
            "fastapi_server:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ]
        
        try:
            self.backend_process = subprocess.Popen(
                cmd,
                cwd=self.script_dir / "piper_api",
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Wait for backend to start
            time.sleep(3)
            
            if self.backend_process.poll() is not None:
                print_error("Backend failed to start")
                return False
                
            # Test backend health
            print_status("Testing backend connection...")
            for i in range(10):
                try:
                    response = requests.get("http://localhost:8000/", timeout=2)
                    if response.status_code == 200:
                        print_success("Backend is running on http://localhost:8000")
                        return True
                except requests.RequestException:
                    pass
                    
                if i == 9:
                    print_error("Backend health check failed")
                    return False
                time.sleep(1)
                
        except Exception as e:
            print_error(f"Failed to start backend: {e}")
            return False
            
    def start_frontend(self):
        """Start the React frontend"""
        print_status("Starting React frontend...")
        
        try:
            self.frontend_process = subprocess.Popen(
                ["npm", "start"],
                cwd=self.script_dir / "piper_frontend",
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Wait for frontend to start
            time.sleep(3)
            
            if self.frontend_process.poll() is not None:
                print_error("Frontend failed to start")
                return False
                
            print_success("Frontend starting on http://localhost:3000")
            return True
            
        except Exception as e:
            print_error(f"Failed to start frontend: {e}")
            return False
            
    def show_status(self):
        """Show application status and available models"""
        print_success("TTS Application started successfully!")
        print()
        print_status("🎤 Piper TTS API: http://localhost:8000")
        print_status("🌐 React Frontend: http://localhost:3000")
        print_status("📚 API Documentation: http://localhost:8000/docs")
        print()
        
        # Show available models
        try:
            response = requests.get("http://localhost:8000/models", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print_status("Available models:")
                for model in data['models']:
                    current = " (CURRENT)" if model['is_current'] else ""
                    print(f"  • {model['name']} ({model['type']}, {model['quality']} quality){current}")
            else:
                print_warning("Unable to fetch model list")
        except:
            print_warning("Unable to fetch model list")
            
        print()
        print_warning("Press Ctrl+C to stop both services")
        
    def run(self):
        """Main run method"""
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.cleanup)
        signal.signal(signal.SIGTERM, self.cleanup)
        
        print_status("Starting TTS Application...")
        print_status(f"Script directory: {self.script_dir}")
        
        # Check requirements
        if not self.check_requirements():
            sys.exit(1)
            
        # Start backend
        if not self.start_backend():
            self.cleanup()
            sys.exit(1)
            
        # Start frontend
        if not self.start_frontend():
            self.cleanup()
            sys.exit(1)
            
        # Show status
        self.show_status()
        
        # Keep running
        try:
            while True:
                time.sleep(1)
                # Check if processes are still running
                if self.backend_process.poll() is not None:
                    print_error("Backend process died")
                    break
                if self.frontend_process.poll() is not None:
                    print_error("Frontend process died")
                    break
        except KeyboardInterrupt:
            pass
            
        self.cleanup()

if __name__ == "__main__":
    app = TTSAppManager()
    app.run()
