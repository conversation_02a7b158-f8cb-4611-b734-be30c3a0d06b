#!/usr/bin/env python3
"""
Prepare Nepali dataset for Piper training according to training.md specifications.
This script converts the ne_np_female dataset to the required format.
"""

import os
import shutil
import csv
from pathlib import Path

def prepare_dataset():
    """Prepare the dataset in the correct format for Piper training."""
    
    # Paths
    source_dir = Path("../dataset/ne_np_female")
    output_dir = Path("training_data")
    
    print(f"Source directory: {source_dir}")
    print(f"Output directory: {output_dir}")
    
    # Check if source exists
    if not source_dir.exists():
        print(f"Error: Source directory {source_dir} does not exist!")
        return False
    
    # Create output directory structure
    output_dir.mkdir(exist_ok=True)
    wav_dir = output_dir / "wav"
    wav_dir.mkdir(exist_ok=True)
    
    # Read the line_index.tsv file
    tsv_file = source_dir / "line_index.tsv"
    if not tsv_file.exists():
        print(f"Error: {tsv_file} does not exist!")
        return False
    
    print(f"Reading {tsv_file}...")
    
    # Prepare metadata for CSV
    metadata_rows = []
    copied_files = 0
    missing_files = 0
    
    with open(tsv_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            # Split by tab
            parts = line.split('\t')
            if len(parts) != 2:
                print(f"Warning: Line {line_num} has {len(parts)} parts, expected 2: {line}")
                continue
                
            filename_without_ext, text = parts
            
            # Source and destination paths
            source_wav = source_dir / "wavs" / f"{filename_without_ext}.wav"
            dest_wav = wav_dir / f"{filename_without_ext}.wav"
            
            # Check if source file exists
            if not source_wav.exists():
                print(f"Warning: Audio file not found: {source_wav}")
                missing_files += 1
                continue
            
            # Copy the audio file
            shutil.copy2(source_wav, dest_wav)
            copied_files += 1
            
            # Add to metadata (format: id|text for single speaker)
            metadata_rows.append([filename_without_ext, text])
            
            if copied_files % 100 == 0:
                print(f"Processed {copied_files} files...")
    
    # Write metadata.csv
    metadata_file = output_dir / "metadata.csv"
    print(f"Writing {metadata_file}...")
    
    with open(metadata_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, delimiter='|')
        for row in metadata_rows:
            writer.writerow(row)
    
    print(f"\nDataset preparation complete!")
    print(f"- Copied {copied_files} audio files")
    print(f"- Missing {missing_files} audio files")
    print(f"- Created metadata.csv with {len(metadata_rows)} entries")
    print(f"- Output directory: {output_dir.absolute()}")
    
    return True

if __name__ == "__main__":
    success = prepare_dataset()
    if success:
        print("\n✅ Dataset ready for Piper training!")
    else:
        print("\n❌ Dataset preparation failed!")
