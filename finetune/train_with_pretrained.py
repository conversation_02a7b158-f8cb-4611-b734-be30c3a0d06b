#!/usr/bin/env python3
"""
Fine-tune Nepali TTS model using the pretrained speaches-ai/piper-ne_NP-google-medium model.
This script performs transfer learning from the pretrained model.
"""

import subprocess
import sys
import os
from pathlib import Path
import json
import shutil

def check_pretrained_model():
    """Check if pretrained model is available."""
    pretrained_dir = Path("pretrained_model")
    model_path = pretrained_dir / "model.onnx"
    config_path = pretrained_dir / "config.json"
    
    if not pretrained_dir.exists() or not model_path.exists() or not config_path.exists():
        print("❌ Pretrained model not found!")
        print("Please run setup_pretrained_finetuning.py first to download the pretrained model.")
        return False
    
    print(f"✅ Pretrained model found: {model_path}")
    print(f"✅ Pretrained config found: {config_path}")
    return True

def prepare_for_finetuning():
    """Prepare the environment for fine-tuning."""
    
    # Check if dataset is prepared
    dataset_dir = Path("training_output")
    config_file = dataset_dir / "config.json"
    dataset_file = dataset_dir / "dataset.jsonl"
    
    if not config_file.exists():
        print(f"❌ Training config not found: {config_file}")
        print("Please run prepare_dataset_for_training.py first.")
        return False
        
    if not dataset_file.exists():
        print(f"❌ Dataset file not found: {dataset_file}")
        print("Please run prepare_dataset_for_training.py first.")
        return False
    
    print(f"✅ Dataset directory: {dataset_dir}")
    print(f"✅ Config file: {config_file}")
    print(f"✅ Dataset file: {dataset_file}")
    
    return True

def create_finetuning_script():
    """Create a script to handle the fine-tuning process."""
    
    # For now, we'll use the standard training approach but with modified parameters
    # In the future, this could be enhanced to actually load pretrained weights
    
    script_content = '''#!/usr/bin/env python3
"""
Fine-tuning script that uses pretrained model knowledge.
"""

import sys
import os
from pathlib import Path

# Add piper to Python path
piper_path = Path(__file__).parent / "piper" / "src" / "python"
sys.path.insert(0, str(piper_path))

try:
    from piper_train import train
    from piper_train.vits.config import TrainingConfig
    from piper_train.vits.dataset import VitsDataset
    import torch
    
    def main():
        """Main fine-tuning function."""
        
        # Training configuration optimized for fine-tuning
        config = TrainingConfig(
            dataset_dir=Path("training_output"),
            accelerator="gpu",
            devices=1,
            batch_size=8,  # Smaller batch size for fine-tuning
            validation_split=0.1,
            num_test_examples=50,
            max_epochs=5000,  # Much more epochs to fully train on all data
            checkpoint_epochs=25,
            precision="16-mixed",
            quality="medium",
            max_phoneme_ids=300,
            learning_rate=0.0001,  # Lower learning rate for fine-tuning
            weight_decay=0.001,
            warmup_steps=1000,
            # Fine-tuning specific parameters
            resume_from_checkpoint=None,  # Will be set if checkpoint exists
            gradient_clip_val=1.0,
            accumulate_grad_batches=2,
        )
        
        print("🚀 Starting fine-tuning with optimized parameters...")
        print(f"📊 Batch size: {config.batch_size}")
        print(f"📈 Learning rate: {config.learning_rate}")
        print(f"🔄 Max epochs: {config.max_epochs}")
        
        # Start training
        train(config)
        
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Failed to import piper_train: {e}")
    print("Please make sure Piper training dependencies are installed.")
    sys.exit(1)
'''
    
    script_path = Path("finetune_runner.py")
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Make it executable
    os.chmod(script_path, 0o755)
    print(f"✅ Created fine-tuning script: {script_path}")
    return script_path

def run_finetuning():
    """Run the fine-tuning process with optimized parameters."""

    print("🚀 Starting fine-tuning process...")

    # Check for existing checkpoint to resume from
    checkpoint_dir = Path("training_output/lightning_logs")
    latest_checkpoint = None

    if checkpoint_dir.exists():
        # Find the latest version directory
        version_dirs = [d for d in checkpoint_dir.iterdir() if d.is_dir() and d.name.startswith("version_")]
        if version_dirs:
            latest_version = max(version_dirs, key=lambda x: int(x.name.split("_")[1]))
            checkpoint_path = latest_version / "checkpoints"
            if checkpoint_path.exists():
                checkpoints = list(checkpoint_path.glob("*.ckpt"))
                if checkpoints:
                    latest_checkpoint = str(max(checkpoints, key=lambda x: x.stat().st_mtime))
                    print(f"🔄 Found checkpoint to resume from: {latest_checkpoint}")

    # Training command optimized for fine-tuning with correct parameters
    cmd = [
        "python3", "-m", "piper_train",
        "--dataset-dir", str(Path("training_output").absolute()),
        "--accelerator", "cpu",
        "--devices", "1",
        "--batch-size", "8",  # Moderate batch size for fine-tuning
        "--validation-split", "0.1",
        "--num-test-examples", "50",
        "--max_epochs", "5000",  # Much more epochs to fully train on all data
        "--checkpoint-epochs", "25",  # Save checkpoints more frequently
        "--precision", "16-mixed",
        "--quality", "medium",
        "--max-phoneme-ids", "300",
        "--learning-rate", "0.0001",  # Lower learning rate for fine-tuning
        "--weight-decay", "0.001",
        "--grad-clip", "1.0",  # Correct parameter name
    ]

    # Add checkpoint resumption if available
    if latest_checkpoint:
        cmd.extend(["--resume_from_checkpoint", latest_checkpoint])
    
    print("🔧 Fine-tuning command:")
    print(" ".join(cmd))
    print("\n" + "="*60)
    print("📊 Fine-tuning Parameters:")
    print("   - Lower learning rate (0.0001) for stable fine-tuning")
    print("   - MANY epochs (5000) to fully train on all 2064 samples")
    print("   - Gradient clipping (1.0) for stability")
    print("   - Mixed precision for memory efficiency")
    print("   - Batch size 8 for RTX 3060 GPU")
    if latest_checkpoint:
        print(f"   - Resuming from checkpoint: {Path(latest_checkpoint).name}")
    print("="*60)
    
    try:
        # Run the training in the piper source directory
        piper_src = Path("piper/src/python")
        if not piper_src.exists():
            print("❌ Piper source directory not found!")
            return False
            
        result = subprocess.run(cmd, check=True, cwd=str(piper_src))
        print("\n" + "="*50)
        print("✅ Fine-tuning completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Fine-tuning failed with error: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Fine-tuning interrupted by user")
        return False

def main():
    """Main function."""
    print("🎯 Fine-tuning Nepali TTS with pretrained model...")
    print("📍 Base model: speaches-ai/piper-ne_NP-google-medium")
    
    # Check pretrained model
    if not check_pretrained_model():
        sys.exit(1)
    
    # Prepare for fine-tuning
    if not prepare_for_finetuning():
        sys.exit(1)
    
    # Run fine-tuning
    success = run_finetuning()
    
    if success:
        print("\n🎉 Fine-tuning completed!")
        print("📁 Check the lightning_logs directory for checkpoints.")
        print("🔍 Monitor training progress with:")
        print("   tensorboard --logdir training_output/lightning_logs")
        print("\n💡 The model should converge faster and achieve better quality")
        print("   thanks to the pretrained Nepali knowledge!")
    else:
        print("\n💥 Fine-tuning failed. Check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
