{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/rmcpantoja/piper/blob/master/notebooks/piper_exportador_modelos_espa%C3%B1ol.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# <font color=\"ffc800\"> **Exportador de modelos de [Piper.](https://github.com/rhasspy/piper)**\n", "## ![Piper logo](https://contribute.rhasspy.org/img/logo.png)\n", "---\n", "* Cuaderno creado por: [rmcpantoja](http://github.com/rmcpantoja)\n", "* Decoración y traducción por: [Xx_Nessu_xX](http://github.com/XxNessuxX)"], "metadata": {"id": "EOL-kjplZYEU"}}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "FfMKr8v2RVOm"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **Instalar software.** 📦\n", "#@markdown ---\n", "\n", "print(\"\\033[93mInstalando...\")\n", "!git clone -q https://github.com/rhasspy/piper\n", "%cd /content/piper/src/python\n", "!pip install pip==24.0\n", "!pip install -q cython>=0.29.0 librosa>=0.9.2 numpy>=1.19.0 pytorch-lightning~=1.7.0 torch~=1.11.0\n", "!pip install -q onnx onnxruntime-gpu\n", "!bash build_monotonic_align.sh\n", "!apt-get install espeak-ng\n", "!pip install -q torchtext==0.12.0\n", "# fixing recent compativility isswes:\n", "!pip install -q torchaudio==0.11.0 torchmetrics==0.11.4\n", "!pip install --upgrade gdown\n", "\n", "print(\"\\033[93mHecho.\")"]}, {"cell_type": "code", "source": ["#@markdown # <font color=\"ffc800\"> **Sección de generación de paquetes de voz.** 🗣️\n", "#@markdown ---\n", "%cd /content/piper/src/python\n", "import os\n", "import json\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "import json\n", "from google.colab import output\n", "guideurl = \"https://github.com/rmcpantoja/piper/blob/master/notebooks/wav/es\"\n", "#@markdown #### *Descargar:*\n", "#@markdown **ID de Drive o enlace de descarga directa del modelo en otra nube:**\n", "model_id = \"\" # @param {type:\"string\"}\n", "#@markdown **ID de Drive o enlace de descarga directa del archivo config.json:**\n", "config_id = \"\" # @param {type:\"string\"}\n", "#@markdown ---\n", "\n", "#@markdown #### *Proceso de creación:*\n", "#@markdown **Elige el código de idioma (formato iso639-1):**\n", "\n", "#@markdown Puedes consultar la lista de códigos y nombres de idiomas [aquí](https://www.loc.gov/standards/iso639-2/php/English_list.php).<br>(es_ES = Español España | es_LA = Español Latino América)\n", "\n", "language = \"es_ES\" #@param [\"ar_J<PERSON>\", \"ca_ES\", \"cs_CZ\", \"da_DK\", \"de_DE\", \"el_GR\", \"en_GB\", \"en_US\", \"es_ES\", \"es_LA\", \"fi_FI\", \"fr_FR\", \"grc\", \"hu_GU\", \"is_IS\", \"it_IT\", \"kk_KZ\", \"ka_GE\", \"lb_LU\", \"nb\", \"ne\", \"nl_BE\", \"no_NO\", \"pl_PL\", \"pt_BR\", \"pt_PT\", \"ro_RO\", \"ru_RU\", \"sk_SK\", \"sr\", \"sv_SE\", \"sw_CD\", \"tr_TR\", \"uk_UA\", \"vi_VN\", \"zh_CN\"]\n", "voice_name = \"\" #@param {type:\"string\"}\n", "voice_name = voice_name.lower()\n", "quality = \"medium\" #@param [\"high\", \"low\", \"medium\", \"x-low\"]\n", "#@markdown **¿Quieres escribir una tarjeta del modelo?** *(Opcional.)*\n", "write_model_card = False #@param {type:\"boolean\"}\n", "\n", "#@markdown **¿Quieres que esta voz tenga una velocidad de respuesta más rápida?**\n", "streaming = False #@param {type:\"boolean\"}\n", "\n", "def start_process(streaming):\n", "    if not os.path.exists(\"/content/project/model.ckpt\"):\n", "        raise Exception(\"No se ha podido descargar el modelo. Asegúrate de que la compartición del archivo está puesto en público.\")\n", "    output.eval_js(f'new Audio(\"{guideurl}/starting.wav?raw=true\").play()')\n", "    if not streaming:\n", "        !python -m piper_train.export_onnx \"/content/project/model.ckpt\" \"{export_voice_path}/{export_voice_name}.onnx\"\n", "    else:\n", "        !python -m piper_train.export_onnx_streaming \"/content/project/model.ckpt\" \"{export_voice_path}\"\n", "    print(\"\\033[93mComprimiendo...\")\n", "    !tar -czvf \"{packages_path}/{export_voice_name}.tar.gz\" -C \"{export_voice_path}\" .\n", "    output.eval_js(f'new Audio(\"{guideurl}/success.wav?raw=true\").play()')\n", "    print(\"\\033[93mHecho!\")\n", "\n", "if not streaming:\n", "    export_voice_name = f\"{language}-{voice_name}-{quality}\"\n", "else:\n", "    export_voice_name = f\"{language}-{voice_name}+RT-{quality}\"\n", "export_voice_path = \"/content/project/voice-\"+export_voice_name\n", "packages_path = \"/content/project/packages\"\n", "if not os.path.exists(export_voice_path):\n", "    os.makedirs(export_voice_path)\n", "if not os.path.exists(packages_path):\n", "    os.makedirs(packages_path)\n", "print(\"\\033[93mDescargando modelo y su configuración...\")\n", "if model_id.startswith(\"1\"):\n", "    !gdown -q \"{model_id}\" -O /content/project/model.ckpt\n", "elif model_id.startswith(\"https://drive.google.com/file/d/\"):\n", "    !gdown -q \"{model_id}\" -O \"/content/project/model.ckpt\" --fuzzy\n", "else:\n", "    !wget \"{model_id}\" -O \"/content/project/model.ckpt\"\n", "if config_id.startswith(\"1\"):\n", "    !gdown -q \"{config_id}\" -O \"{export_voice_path}/{export_voice_name}.onnx.json\"\n", "elif config_id.startswith(\"https://drive.google.com/file/d/\"):\n", "    !gdown -q \"{config_id}\" -O \"{export_voice_path}/{export_voice_name}.onnx.json\" --fuzzy\n", "else:\n", "    !wget \"{config_id}\" -O \"{export_voice_path}/{export_voice_name}.onnx.json\"\n", "\n", "if os.path.exists(f\"{export_voice_path}/{export_voice_name}.onnx.json\") and streaming:\n", "    with open(f\"{export_voice_path}/{export_voice_name}.onnx.json\", \"r\", encoding=\"utf-8\") as f:\n", "        tmp = f.read()\n", "    new_config = json.loads(tmp)\n", "    new_config[\"streaming\"] = True\n", "    new_config[\"key\"] = export_voice_name\n", "\n", "    with open(f\"{export_voice_path}/{export_voice_name}.onnx.json\", \"w\", encoding=\"utf-8\") as f_new:\n", "        json.dump(new_config, f_new, indent=4)\n", "\n", "if write_model_card:\n", "    with open(f\"{export_voice_path}/{export_voice_name}.onnx.json\", \"r\") as file:\n", "        config = json.load(file)\n", "    sample_rate = config[\"audio\"][\"sample_rate\"]\n", "    num_speakers = config[\"num_speakers\"]\n", "    output.eval_js(f'new Audio(\"{guideurl}/waiting.wav?raw=true\").play()')\n", "    text_area = widgets.Textarea(\n", "        description = \"Rellena la siguiente plantilla y pulsa Iniciar para generar el paquete de voz:\",\n", "        value=f'# Tarjeta de Modelo para {voice_name} ({quality})\\n\\n* Idioma: {language} (normalizado)\\n* Hablantes: {num_speakers}\\n* Calidad: {quality}\\n* Frecuencia de muestreo: {sample_rate}Hz\\n\\n## Dataset\\n\\n* URL: \\n* Licencia: \\n\\n## Entrenamiento\\n\\nEntrenado de 0.\\nAjustado de la voz: ',\n", "        layout=widgets.Layout(width='500px', height='200px')\n", "    )\n", "    button = widgets.<PERSON><PERSON>(description='Iniciar.')\n", "\n", "    def create_model_card(button):\n", "        model_card_text = text_area.value.strip()\n", "        with open(f'{export_voice_path}/MODEL_CARD', 'w') as file:\n", "            file.write(model_card_text)\n", "        text_area.close()\n", "        button.close()\n", "        output.clear()\n", "        start_process(streaming)\n", "\n", "    button.on_click(create_model_card)\n", "\n", "    display(text_area, button)\n", "else:\n", "    start_process(streaming)\n"], "metadata": {"cellView": "form", "id": "zjcKghmaOaOP"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["#@markdown # <font color=\"ffc800\"> **Descarga/exporta tu paquete de voz generado.** 📥\n", "#@markdown ---\n", "\n", "#@markdown #### *¿Cómo quieres exportar tu modelo?*\n", "export_mode = \"Subirlo a mi Google Drive.\" #@param [\"<PERSON><PERSON><PERSON> el paquete de voz en mi dispositivo. (Puede llevar algún tiempo.)\", \"Subirlo a mi Google Drive.\"]\n", "print(\"\\033[93mExportando paquete...\")\n", "if export_mode == \"<PERSON>cargar el paquete de voz en mi dispositivo. (Puede llevar algún tiempo.)\":\n", "    from google.colab import files\n", "    files.download(f\"{packages_path}/voice-{export_voice_name}.tar.gz\")\n", "    msg = \"\\033[93mEspera un momento mientras se descarga el paquete.\"\n", "else:\n", "    voicepacks_folder = \"/content/drive/MyDrive/piper voice packages\"\n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "    if not os.path.exists(voicepacks_folder):\n", "        os.makedirs(voicepacks_folder)\n", "    !cp \"{packages_path}/voice-{export_voice_name}.tar.gz\" \"{voicepacks_folder}\"\n", "    msg = f\"\\033[93mPuedes encontrar el paquete de voz generado en: {voicepacks_folder}.\"\n", "print(f\"\\033[93mHecho. {msg}\")"], "metadata": {"cellView": "form", "id": "Hu3V9CJeWc4Y"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# \"*¡Quiero probar este modelo! ¿Ya no necesito nada más?*\"\n", "\n", "No, ¡esto está casi terminado! Ahora puedes compartir tu paquete generado con tus amigos, subirlo a un almacenamiento en la nube y/o probarlo en:\n", "\n", "* [El cuaderno de inferencia](https://colab.research.google.com/github/rmcpantoja/piper/blob/master/notebooks/piper_inferencia_espa%C3%B1ol_(ONNX).ipynb)\n", "  * Ejecuta las celdas en orden para que funcione correctamente, al igual que con todos los cuadernos. Además, el cuaderno de inferencia te guiará a través del proceso utilizando la función de accesibilidad mejorada si lo deseas. Es fácil de usar. ¡Pruébalo!\n", "* O a través del lector de pantalla NVDA.\n", "  * Descarga e instala la última versión del [complemento](https://github.com/mush42/piper-nvda/releases).\n", "* Una vez instalado el complemento, dirígete al menú NVDA/gestor de voces...\n", "  * En la página de voces instaladas, tabula hasta encontrar el botón `Install from local file`, pulsa enter y selecciona el paquete generado en tus descargas.\n", "  * Una vez que el paquete esté seleccionado e instalado, aplica los cambios y reinicia NVDA para actualizar la lista de voces.\n", "* ¡Disfruta de tu creación!\n"], "metadata": {"id": "IRiNBHkeoDbC"}}]}