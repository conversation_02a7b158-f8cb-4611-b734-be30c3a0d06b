{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/rmcpantoja/piper/blob/master/notebooks/piper_multilenguaje_cuaderno_de_entrenamiento.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "eK3nmYDB6C1a"}, "source": ["# <font color=\"ffc800\"> **Cuaderno de entrenamiento de [Piper.](https://github.com/rhasspy/piper)**\n", "## ![Piper logo](https://contribute.rhasspy.org/img/logo.png)\n", "\n", "---\n", "\n", "- <PERSON><PERSON><PERSON><PERSON> creado por [rmcpantoja](http://github.com/rmcpantoja)\n", "- Colaborador y traductor: [Xx_Nessu_xX](http://github.com/Xx_Nessu_xX)\n", "\n", "---\n", "\n", "# Notas:\n", "\n", "- <font color=\"orange\">**Las cosas en naranja significa que son importantes.**\n", "\n", "# Créditos:\n", "\n", "* [Feanix-Fyre fork](https://github.com/Feanix-Fyre/piper) con algunas mejoras.\n", "* [Tacotron2 NVIDIA training notebook](https://github.com/justinjohn0306/FakeYou-Tacotron2-Notebook) - Fragmento de duración del conjunto de datos.\n", "* [🐸TTS](https://github.com/coqui-ai/TTS) - Demostración del remuestreador y del formador XTTS."]}, {"cell_type": "markdown", "metadata": {"id": "AICh6p5OJybj"}, "source": ["# <font color=\"ffc800\">🔧 ***Primeros pasos.*** 🔧"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "qyxSMuzjfQrz"}, "outputs": [], "source": ["#@markdown ## <font color=\"ffc800\"> **Google Colab Anti-Disconnect.** 🔌\n", "#@markdown ---\n", "#@markdown #### Evita la desconexión automática. Aún así, se desconectará después de <font color=\"orange\">**6 a 12 horas**</font>.\n", "\n", "import IPython\n", "js_code = '''\n", "function ClickConnect(){\n", "console.log(\"Working\");\n", "document.querySelector(\"colab-toolbar-button#connect\").click()\n", "}\n", "setInterval(ClickConnect,60000)\n", "'''\n", "display(IPython.display.Javascript(js_code))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "ygxzp-xHTC7T"}, "outputs": [], "source": ["#@markdown ## <font color=\"ffc800\"> **Comprueba la GPU.** 👁️\n", "#@markdown ---\n", "#@markdown #### Una GPU de mayor capacidad puede aumentar la velocidad de entrenamiento. Por defecto, tendr<PERSON> una <font color=\"orange\">**Tesla T4**</font>.\n", "!nvidia-smi"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "sUNjId07JfAK"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **Monta tu Google Drive.** 📂\n", "#@markdown ---\n", "from google.colab import drive\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "_XwmTVlcUgCh"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **Instalar software.** 📦\n", "#@markdown ---\n", "\n", "#@markdown ####En esta celda se instalará el sintetizador y sus dependencias necesarias para ejecutar el entrenamiento. (Esto puede llevar un rato.)\n", "\n", "# clone:\n", "!git clone -q https://github.com/rmcpantoja/piper\n", "%cd /content/piper/src/python\n", "!wget -q \"https://raw.githubusercontent.com/coqui-ai/TTS/dev/TTS/bin/resample.py\"\n", "!pip install pip==24.0\n", "!pip install -q -r requirements.txt\n", "!pip install -q cython>=0.29.0 piper-phonemize==1.1.0 librosa>=0.9.2 numpy==1.24 onnxruntime>=1.11.0 pytorch-lightning==1.7.7 torch==1.13.0+cu117 --extra-index-url https://download.pytorch.org/whl/cu117\n", "!pip install -q torchtext==0.14.0 torchvision==0.14.0\n", "# fixing recent compativility isswes:\n", "!pip install -q torchaudio==0.13.0 torchmetrics==0.11.4 faster_whisper\n", "!pip install --upgrade gdown transformers\n", "!bash build_monotonic_align.sh\n", "# Useful vars:\n", "use_whisper = True\n", "print(\"\\033[93mHecho.\")"]}, {"cell_type": "markdown", "metadata": {"id": "A3bMzEE0V5Ma"}, "source": ["# <font color=\"ffc800\"> 🤖 ***Entrenamiento.*** 🤖"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "SvEGjf0aV8eg"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **1. Extraer dataset.** 📥\n", "#@markdown ---\n", "#@markdown ####Importante: los audios deben estar en formato <font color=\"orange\">**wav, (16000 o 22050hz, 16-bits, mono), y, por comodidad, numerados.<br>Ejemplo:**\n", "\n", "#@markdown * <font color=\"orange\">**1.wav**</font>\n", "#@markdown * <font color=\"orange\">**2.wav**</font>\n", "#@markdown * <font color=\"orange\">**3.wav**</font>\n", "#@markdown * <font color=\"orange\">**.....**</font>\n", "\n", "#@markdown ---\n", "import os\n", "import wave\n", "import zipfile\n", "import datetime\n", "\n", "def get_dataset_duration(wav_path):\n", "    totalduration = 0\n", "    for file_name in [x for x in os.listdir(wav_path) if os.path.isfile(x) and \".wav\" in x]:\n", "        with wave.open(file_name, \"rb\") as wave_file:\n", "            frames = wave_file.getnframes()\n", "            rate = wave_file.getframerate()\n", "            duration = frames / float(rate)\n", "            totalduration += duration\n", "    wav_count = len(os.listdir(wav_path))\n", "    duration_str = str(datetime.timedelta(seconds=round(totalduration, 0)))\n", "    return wav_count, duration_str\n", "\n", "%cd /content\n", "if not os.path.exists(\"/content/dataset\"):\n", "    os.makedirs(\"/content/dataset\")\n", "    os.makedirs(\"/content/dataset/wavs\")\n", "%cd /content/dataset\n", "#@markdown ### Ruta del dataset para descomprimir:\n", "zip_path = \"/content/drive/MyDrive/wavs.zip\" #@param {type:\"string\"}\n", "zip_path = zip_path.strip()\n", "if zip_path:\n", "    if os.path.exists(zip_path):\n", "        if zipfile.is_zipfile(zip_path):\n", "            print(\"Descomprimiendo audios...\")\n", "            !unzip -q -j \"{zip_path}\" -d /content/dataset/wavs\n", "        else:\n", "            print(\"Copiando audios en su directorio...\")\n", "            fp = zip_path + \"/.\"\n", "            !cp -a \"$fp\" \"/content/dataset/wavs\"\n", "    else:\n", "        raise Exception(\"La ruta proporcionada para los wavs no es correcta. Por favor, introduzca una ruta válida.\")\n", "else:\n", "    raise Exception(\"Debes proporcionar una ruta a los wavs.\")\n", "if os.path.exists(\"/content/dataset/wavs/wavs\"):\n", "    for file in os.listdir(\"/content/dataset/wavs/wavs\"):\n", "        !mv /content/dataset/wavs/wavs/\"$file\"  /content/dataset/wavs/\"$file\"\n", "    !rm -r /content/dataset/wavs/*.txt\n", "    !rm -r /content/dataset/wavs/*.csv\n", "%cd /content/dataset/wavs\n", "audio_count, dataset_dur = get_dataset_duration(\"/content/dataset/wavs\")\n", "print(f\"Conjunto de datos abierto con {audio_count} wavs con una duración de {dataset_dur}.\")\n", "%cd ..\n", "#@markdown ---"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "E0W0OCvXXvue"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **2. <PERSON>gar el archivo de transcripción.** 📝\n", "#@markdown ---\n", "#@markdown ####<font color=\"orange\">**Importante: la transcripción significa escribir lo que dice el personaje en cada uno de los audios, y debe tener la siguiente estructura:**\n", "\n", "#@markdown ##### <font color=\"orange\">Para un conjunto de datos de un solo hablante:\n", "#@markdown * wavs/1.wav|Esto dice el personaje en el audio 1.\n", "#@markdown * wavs/2.wav|<PERSON><PERSON>, el texto que dice el personaje en el audio 2.\n", "#@markdown * ...\n", "\n", "#@markdown ##### <font color=\"orange\">Para un conjunto de datos de varios hablantes:\n", "\n", "#@markdown * wavs/speaker1audio1.wav|speaker1|Esto es lo que dice el primer hablante.\n", "#@markdown * wavs/speaker1audio2.wav|speaker1|Este es otro audio del primer hablante.\n", "#@markdown * wavs/speaker2audio1.wav|speaker2|Esto es lo que dice el segundo hablante en el primer audio.\n", "#@markdown * wavs/speaker2audio2.wav|speaker2|Este es otro audio del segundo hablante.\n", "#@markdown * ...\n", "\n", "#@markdown #### Y así sucesivamente. Además, la transcripción debe estar en formato <font color=\"orange\">**.csv o también sirve en .txt (UTF-8 sin BOM)**\n", "#@markdown ---\n", "#@markdown ## <font color=\"orange\">**![¡NUEVO!](https://s9.gifyu.com/images/SUvXW.gif) Auto-transcripción con Whisper IA si no se proporciona la transcripción.**\n", "\n", "#@markdown ####**Nota: Si no subes ningún archivo de transcripción, los wavs se transcribirán utilizando la herramienta Whisper cuando ejecutes el siguiente paso. Después, el bloc de notas continuará con el resto del preprocesamiento si no hay errores. Aunque la herramienta Whisper tiene buenos resultados de transcripción, en mi experiencia recomiendo transcribir manualmente y subirlo desde esta celda, ya que una buena voz TTS necesita ser optimizada para dar aún mejores resultados. Por ejemplo, al transcribir manualmente podrás observar cada detalle que hace el hablante (como puntuación, sonidos, etc.), y plasmarlos en la transcripción de acuerdo a las entonaciones del hablante.**\n", "#@markdown ---\n", "%cd /content/dataset\n", "from google.colab import files\n", "!rm /content/dataset/metadata.csv\n", "listfn, length = files.upload().popitem()\n", "if listfn != \"metadata.csv\":\n", "  !mv \"$listfn\" metadata.csv\n", "use_whisper = False\n", "%cd .."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "dOyx9Y6JYvRF"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **3. Preprocesar el dataset.** 🔄\n", "#@markdown ---\n", "import os\n", "if use_whisper:\n", "    import torch\n", "    from faster_whisper import WhisperModel\n", "    from tqdm import tqdm\n", "    from google import colab\n", "\n", "    device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "    print(f\"Utilizar el dispositivo: {device}\")\n", "\n", "    def make_dataset(path, language):\n", "        metadata = \"\"\n", "        text = \"\"\n", "        files = [f for f in os.listdir(path) if f.endswith(\".wav\")]\n", "        assert len(files) > 0, \"¡Tampoco has subido los wavs! Por favor, sube al menos un zip con los wavs en el paso 2.\"\n", "        metadata_file = open(f\"{path}/../metadata.csv\", \"w\")\n", "        whisper = WhisperModel(\"large-v3\", device=device, compute_type=\"float16\")\n", "        for audio_file in tqdm(files):\n", "            full_path = os.path.join(path, audio_file)\n", "            segments, _ = whisper.transcribe(full_path, word_timestamps=False, language=language)\n", "            for segment in segments:\n", "                text += segment.text\n", "            text = text.strip()\n", "            text = text.replace('\\n', ' ')\n", "            metadata = f\"{audio_file}|{text}\\n\"\n", "            metadata_file.write(metadata)\n", "            text = \"\"\n", "        colab.files.download(f\"{path}/../metadata.csv\")\n", "        del whisper\n", "        return True\n", "#@markdown ### En primer lugar, seleccione el idioma de su conjunto de datos. <br> (Está disponible para español los siguientes: Español castellano y Español lationamericano.)\n", "language = \"<PERSON>spa<PERSON><PERSON> (Castellano)\" #@param [\"ألعَرَبِي\", \"Català\", \"če<PERSON><PERSON>\", \"Dansk\", \"<PERSON>utsch\", \"Ελληνικά\", \"English (British)\", \"English (U.S.)\", \"<PERSON><PERSON><PERSON><PERSON><PERSON> (Castellano)\", \"<PERSON><PERSON><PERSON><PERSON><PERSON> (Latinoamericano)\", \"Suomi\", \"Français\", \"Magyar\", \"Icelandic\", \"Italiano\", \"ქართული\", \"қазақша\", \"Lëtzebuergesch\", \"नेपाली\", \"Nederlands\", \"Norsk\", \"Polski\", \"Português (Brasil)\", \"Português (Portugal)\", \"Română\", \"Русский\", \"Српски\", \"Svenska\", \"Kiswahili\", \"Türkçe\", \"украї́нська\", \"Tiếng Việt\", \"简体中文\"]\n", "#@markdown ---\n", "# language definition:\n", "languages = {\n", "    \"ألعَرَبِي\": \"ar\",\n", "    \"Català\": \"ca\",\n", "    \"čeština\": \"cs\",\n", "    \"Dansk\": \"da\",\n", "    \"Deutsch\": \"de\",\n", "    \"Ελληνικά\": \"el\",\n", "    \"English (British)\": \"en\",\n", "    \"English (U.S.)\": \"en-us\",\n", "    \"Español (Castellano)\": \"es\",\n", "    \"Español (Latinoamericano)\": \"es-419\",\n", "    \"Suomi.\": \"fi\",\n", "    \"Français\": \"fr\",\n", "    \"Magyar\": \"hu\",\n", "    \"Icelandic\": \"is\",\n", "    \"Italiano\": \"it\",\n", "    \"ქართული\": \"ka\",\n", "    \"қазақша\": \"kk\",\n", "    \"Lëtzebuergesch\": \"lb\",\n", "    \"नेपाली\": \"ne\",\n", "    \"Nederlands\": \"nl\",\n", "    \"Norsk\": \"nb\",\n", "    \"Polski\": \"pl\",\n", "    \"Português (Brasil)\": \"pt-br\",\n", "    \"Português (Portugal)\": \"pt-pt\",\n", "    \"Română\": \"ro\",\n", "    \"Русский\": \"ru\",\n", "    \"Српски\": \"sr\",\n", "    \"Svenska\": \"sv\",\n", "    \"Kiswahili\": \"sw\",\n", "    \"Türkçe\": \"tr\",\n", "    \"украї́нська\": \"uk\",\n", "    \"Tiếng Việt\": \"vi\",\n", "    \"简体中文\": \"zh\"\n", "}\n", "\n", "def _get_language(code):\n", "    return languages[code]\n", "\n", "final_language = _get_language(language)\n", "#@markdown ### Elige un nombre para tu modelo:\n", "model_name = \"Test\" #@param {type:\"string\"}\n", "#@markdown ---\n", "# output:\n", "#@markdown ###<PERSON><PERSON> la <PERSON>a de trabajo: (se recomienda guardar en Drive)\n", "\n", "#@markdown La carpeta de trabajo se utilizará en el preprocesamiento, pero también en el entrenamiento del modelo.\n", "output_path = \"/content/drive/MyDrive/colab/piper\" #@param {type:\"string\"}\n", "output_dir = output_path+\"/\"+model_name\n", "if not os.path.exists(output_dir):\n", "  os.makedirs(output_dir)\n", "#@markdown ---\n", "#@markdown ### Elige el formato del dataset:\n", "dataset_format = \"ljspeech\" #@param [\"ljspeech\", \"mycroft\"]\n", "#@markdown ---\n", "#@markdown ### ¿Se trata de un conjunto de datos de un solo hablante? Si no es así, desmarca la casilla:\n", "single_speaker = True #@param {type:\"boolean\"}\n", "if single_speaker:\n", "  force_sp = \" --single-speaker\"\n", "else:\n", "  force_sp = \"\"\n", "#@markdown ---\n", "#@markdown ###Seleccione la frecuencia de muestreo del dataset:\n", "sample_rate = \"22050\" #@param [\"16000\", \"22050\"]\n", "#@markdown ---\n", "!mkdir /content/audio_cache\n", "%cd /content/piper/src/python\n", "#@markdown ###¿Quieres entrenar utilizando esta frecuencia de muestreo, pero tus audios no la tienen?\n", "#@markdown ¡El remuestreador te ayuda a hacerlo rápidamente!\n", "resample = False #@param {type:\"boolean\"}\n", "if resample:\n", "  !python resample.py --input_dir \"/content/dataset/wavs\" --output_dir \"/content/dataset/wavs_resampled\" --output_sr {sample_rate} --file_ext \"wav\"\n", "  !mv /content/dataset/wavs_resampled/* /content/dataset/wavs\n", "#@markdown ---\n", "if use_whisper:\n", "    print(\"El archivo de transcripción no se ha cargado. Transcribiendo estos audios usando Whisper...\")\n", "    make_dataset(\"/content/dataset/wavs\", final_language[:2])\n", "    print(\"¡Transcripción realizada! Pre-procesando...\")\n", "!python -m piper_train.preprocess \\\n", "  --language {final_language} \\\n", "  --input-dir /content/dataset \\\n", "  --cache-dir \"/content/audio_cache\" \\\n", "  --output-dir \"{output_dir}\" \\\n", "  --dataset-name \"{model_name}\" \\\n", "  --dataset-format {dataset_format} \\\n", "  --sample-rate {sample_rate} \\\n", "  {force_sp}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ickQlOCRjkBL", "cellView": "form"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **4. <PERSON><PERSON><PERSON><PERSON>.** 🧰\n", "#@markdown ---\n", "import json\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "from google.colab import output\n", "import os\n", "#@markdown ### <font color=\"orange\">**Seleccione la acción para entrenar este conjunto de datos: (LEER ANTENTAMENTE.)**\n", "\n", "#@markdown * La opción de <font color=\"orange\">continuar un entrenamiento</font> se explica por sí misma. Si has entrenado previamente un modelo con colab gratuito, se te ha acabado el tiempo y estás considerando entrenarlo un poco más, esto es ideal para ti. S<PERSON>lo tienes que establecer los mismos ajustes que estableciste cuando entrenaste este modelo por primera vez.\n", "#@markdown * La opción para <font color=\"orange\">convertir un modelo de un solo hablante en un modelo multihablante</font> se explica por sí misma, y para ello es importante que hayas procesado un conjunto de datos que contenga texto y audio de todos los posibles hablantes que quieras entrenar en tu modelo.\n", "#@markdown * La opción <font color=\"orange\">finetune</font> se utiliza para entrenar un conjunto de datos utilizando un modelo preentrenado, es decir, entrenar sobre esos datos. <font color=\"orange\">Esta opción es necesaria para entrenar cualquier voz, ya tengas un dataset pequeño o amplio. *(Se recomiendan más de cinco minutos de datos.)*</font>\n", "#@markdown * La opción <font color=\"orange\">entrenar desde cero</font> se usa para hacer modelos base, o sea, construye características como el diccionario y la forma del habla desde cero, y esto puede tardar más en converger. Para ello, se recomiendan horas de audio *(8 como mínimo)* que tengan una gran colección de fonemas.\n", "action = \"Finetune.\" #@param [\"Continuar entrenando.\", \"Convertir modelo de un solo hablante a un modelo de multi hablante.\", \"Finetune.\", \"Entrenar desde cero.\"]\n", "#@markdown ---\n", "if action == \"Continuar entrenando.\":\n", "    if os.path.exists(f\"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt\"):\n", "        ft_command = f'--resume_from_checkpoint \"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt\" '\n", "        print(f\"\\033[93mContinuar entrenamiento de {model_name} desde: {output_dir}/lightning_logs/version_0/checkpoints/last.ckpt\")\n", "    else:\n", "        raise Exception(\"El entrenamiento no puede continuar ya que no hay ningún punto de control en el que continuar.\")\n", "elif action == \"Finetune.\":\n", "    if os.path.exists(f\"{output_dir}/lightning_logs/version_0/checkpoints/last.ckpt\"):\n", "        raise Exception(\"¡Oh no! Ya has entrenado este modelo anteriormente, no puedes elegir esta opción ya que tu progreso se perderá, y entonces tu tiempo anterior no contará. Por favor, selecciona la opción para continuar un entrenamiento.\")\n", "    else:\n", "        ft_command = '--resume_from_checkpoint \"/content/pretrained.ckpt\" '\n", "elif action == \"Convertir modelo de un solo hablante a un modelo de multi hablante.\":\n", "    if not single_speaker:\n", "        ft_command = '--resume_from_single_speaker_checkpoint \"/content/pretrained.ckpt\" '\n", "    else:\n", "        raise Exception(\"Este conjunto de datos no es multihablante.\")\n", "else:\n", "    ft_command = \"\"\n", "if action== \"Convertir modelo de un solo hablante a un modelo de multi hablante.\" or action == \"Finetune.\":\n", "    try:\n", "        with open('/content/piper/notebooks/pretrained_models.json') as f:\n", "            pretrained_models = json.load(f)\n", "        if final_language in pretrained_models:\n", "            models = pretrained_models[final_language]\n", "            model_options = [(model_name, model_name) for model_name, model_url in models.items()]\n", "            model_dropdown = widgets.Dropdown(description = \"Seleccionar modelo preinstalado.\", options=model_options)\n", "            download_button = widgets.Button(description=\"Descargar\")\n", "            def download_model(btn):\n", "                model_name = model_dropdown.value\n", "                model_url = pretrained_models[final_language][model_name]\n", "                print(\"\\033[93mDescargando modelo preentrenado.\")\n", "                if model_url.startswith(\"1\"):\n", "                    !gdown -q \"{model_url}\" -O \"/content/pretrained.ckpt\"\n", "                elif model_url.startswith(\"https://drive.google.com/file/d/\"):\n", "                    !gdown -q \"{model_url}\" -O \"/content/pretrained.ckpt\" --fuzzy\n", "                else:\n", "                    !wget -q \"{model_url}\" -O \"/content/pretrained.ckpt\"\n", "                model_dropdown.close()\n", "                download_button.close()\n", "                output.clear()\n", "                if os.path.exists(\"/content/pretrained.ckpt\"):\n", "                    print(\"\\033[93m¡Modelo descargado!\")\n", "                else:\n", "                    raise Exception(\"No se pudo descargar el modelo preentrenado.\")\n", "            download_button.on_click(download_model)\n", "            display(model_dropdown, download_button)\n", "        else:\n", "            raise Exception(f\"No hay modelos preentrenados disponibles para el idioma {final_language}\")\n", "    except FileNotFoundError:\n", "        raise Exception(\"No se ha encontrado el archivo pretrained_models.json.\")\n", "else:\n", "    print(\"\\033[93mAdvertencia: este modelo será entrenado desde cero. Necesitas al menos 8 horas de datos para que todo funcione decentemente. Mucha suerte.\")\n", "#@markdown ### <PERSON><PERSON> el tamaño del lote basándose en este conjunto de datos:\n", "batch_size = 12 #@param {type:\"integer\"}\n", "#@markdown ---\n", "\n", "#@markdown ### Elige la calidad para este modelo:\n", "\n", "#@markdown * x-low - 16Khz audio, 5-7M params\n", "#@markdown * medium - 22.05Khz audio, 15-20 params\n", "#@markdown * high - 22.05Khz audio, 28-32M params\n", "quality = \"medium\" #@param [\"high\", \"x-low\", \"medium\"]\n", "#@markdown ---\n", "#@markdown ### ¿Cada cuántas épocas quieres autoguardar los puntos de control de entrenamiento?\n", "#@markdown Cuanto mayor sea tu conjunto de datos, debes establecer este intervalo de guardado en un valor menor, ya que las épocas pueden progresar durante más tiempo.\n", "checkpoint_epochs = 5 #@param {type:\"integer\"}\n", "#@markdown ---\n", "#@markdown ### Intervalo para guardar los k mejores modelos:\n", "#@markdown Póngalo a 0 si desea desactivar el guardado de varios modelos. Si este es el caso, marque la casilla de abajo. Si se establece en 1, los modelos se guardarán con el nombre de archivo epoch=xx-step=xx.ckpt, por lo que tendrá que vaciar la papelera de Drive cada cierto tiempo.\n", "num_ckpt = 0 #@param {type: \"integer\"}\n", "#@markdown ---\n", "#@markdown ### <font color=\"orange\">**Guardar el último modelo:**\n", "#@markdown <font color=\"orange\">Esta casilla debe estar marcada si deseas guardar un único modelo (last.ckpt). Guardar un único modelo sólo se aplica si num_ckpt es igual a 0. Si es así, el parámetro intervalo de pasos para autoguardar se ignora, ya que se guarda el último modelo por época; además, no tendrás que preocuparte por el almacenamiento. Siendo igual a 1, se guardará último.ckpt, pero otro modelo (model_vVersion.ckpt, este último tiene en cuenta el intervalo de épocas que establezcas), por lo que tendrías que vaciar la papelera a menudo.\n", "#@markdown <font color=\"orange\">No es recomendable usar esta opción en datasets ***extremadamente pequeños***, ya que al guardar el último modelo cada época, este proceso será muy rápido y el entrenador no podrá guardar el modelo completo, lo que resultaría en un last.ckpt. ***corrupto***\n", "save_last = True # @param {type: \"boolean\"}\n", "#@markdown ---\n", "#@markdown ### Intervalo de pasos para generar muestras de audio del modelo:\n", "log_every_n_steps = 1000 #@param {type:\"integer\"}\n", "#@markdown ---\n", "#@markdown ### Número de épocas para el entrenamiento.\n", "max_epochs = 10000 #@param {type:\"integer\"}\n", "#@markdown ---\n"]}, {"cell_type": "code", "source": ["#@markdown # <font color=\"ffc800\"> **5. Ejecuta la extensión TensorBoard.** 📈\n", "#@markdown ---\n", "#@markdown #### El TensorBoard se utiliza para visualizar los resultados del modelo mientras está siendo entrenado como el audio y las pérdidas.\n", "%load_ext tensorboard\n", "%tensorboard --logdir {output_dir}"], "metadata": {"cellView": "form", "id": "IwmvIoVYKnxB"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"background_save": true}, "id": "X4zbSjXg2J3N"}, "outputs": [], "source": ["#@markdown # <font color=\"ffc800\"> **6. <PERSON><PERSON><PERSON>.** 🏋️‍♂️\n", "#@markdown ---\n", "#@markdown #### Ejecuta esta celda para entrenar tu modelo.\n", "\n", "#@markdown ---\n", "\n", "#@markdown ### <font color=\"orange\">**¿Desactivar la validación?**\n", "#@markdown Desmarcando esta casilla, permitirás entrenar el dataset completo, sin utilizar ningún archivo de audio o ejemplo como conjunto de validación. Por lo tanto, no será capaz de generar audios en el tensorboard mientras se está entrenando. Se recomienda desactivar la validación en tu dataset si es ***extremadamente pequeños.***\n", "validation = True #@param {type:\"boolean\"}\n", "if validation:\n", "    validation_split = 0.01\n", "    num_test_examples = 1\n", "else:\n", "    validation_split = 0\n", "    num_test_examples = 0\n", "if not save_last:\n", "    save_last_command = \"\"\n", "else:\n", "    save_last_command = \"--save_last True \"\n", "get_ipython().system(f'''\n", "python -m piper_train \\\n", "--dataset-dir \"{output_dir}\" \\\n", "--accelerator 'gpu' \\\n", "--devices 1 \\\n", "--batch-size {batch_size} \\\n", "--validation-split {validation_split} \\\n", "--num-test-examples {num_test_examples} \\\n", "--quality {quality} \\\n", "--checkpoint-epochs {checkpoint_epochs} \\\n", "--num_ckpt {num_ckpt} \\\n", "{save_last_command}\\\n", "--log_every_n_steps {log_every_n_steps} \\\n", "--max_epochs {max_epochs} \\\n", "{ft_command}\\\n", "--precision 32\n", "''')"]}, {"cell_type": "markdown", "metadata": {"id": "6ISG085SYn85"}, "source": ["# <font color=\"orange\"> **¿Has terminado el entrenamiento y quieres probar el modelo?**\n", "\n", "* ¡Si quieres ejecutar este modelo en cualquier software que Piper integre o en la misma app de Piper, exporta tu modelo usando el [cuaderno exportador de modelos](https://colab.research.google.com/github/rmcpantoja/piper/blob/master/notebooks/piper_exportador_modelos_espa%C3%B1ol.ipynb)!\n", "* Si quieres probar este modelo ahora mismo antes de exportarlo al formato soportado por Piper.<br>¡Prueba tu last.ckpt generado con [este cuaderno](https://colab.research.google.com/github/rmcpantoja/piper/blob/master/notebooks/piper_inferencia_espa%C3%B1ol(ckpt).ipynb)!"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": [], "include_colab_link": true}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}