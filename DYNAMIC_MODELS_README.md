# Dynamic TTS Model Management System

This system provides dynamic model detection and switching for the Piper TTS API with an intuitive frontend interface.

## 🎯 Features

- **Dynamic Model Detection**: Automatically scans and detects available models
- **Easy Model Switching**: Switch between models via API or frontend interface
- **Model Metadata**: Rich information about each model (quality, type, size, etc.)
- **Organized Structure**: Clean folder organization for different model types
- **Real-time Updates**: Frontend automatically updates when models are added/removed

## 📁 Model Directory Structure

```
piper_api/models/
├── original_google/          # Original Google pre-trained model
│   ├── model.onnx
│   ├── config.json
│   └── metadata.json
├── pretrained_checkpoint/    # Converted pretrained checkpoint
│   ├── model.onnx
│   ├── config.json
│   └── metadata.json
├── finetuned/               # Your fine-tuned model (2839 epochs)
│   ├── model.onnx
│   ├── config.json
│   └── metadata.json
└── your_new_model/          # Add new models here
    ├── model.onnx           # Required: ONNX model file
    ├── config.json          # Required: Piper config file
    └── metadata.json        # Optional: Model metadata
```

## 🚀 Quick Start

### Option 1: <PERSON><PERSON> (Linux/macOS)
```bash
./start_tts_app.sh
```

### Option 2: Python Script (Cross-platform)
```bash
python start_tts_app.py
```

### Option 3: Manual Start
```bash
# Terminal 1 - Backend
cd piper_api
../.venv/bin/python -m uvicorn fastapi_server:app --host 0.0.0.0 --port 8000

# Terminal 2 - Frontend
cd piper_frontend
npm start
```

## 🎤 Available Models

### 1. Fine-tuned Model (Recommended)
- **Type**: Fine-tuned
- **Quality**: High
- **Training**: 2839 epochs with custom Nepali dataset
- **Best for**: Production use with custom voice characteristics

### 2. Original Google Model
- **Type**: Pre-trained
- **Quality**: Medium
- **Training**: Google's original Nepali dataset
- **Best for**: General purpose Nepali TTS

### 3. Pretrained Checkpoint
- **Type**: Checkpoint
- **Quality**: Medium
- **Training**: Base pretrained model before fine-tuning
- **Best for**: Baseline comparison

## 🔧 Adding New Models

1. **Create Model Directory**:
   ```bash
   mkdir piper_api/models/my_new_model
   ```

2. **Add Required Files**:
   - `model.onnx` - Your ONNX model file
   - `config.json` - Piper configuration file

3. **Add Metadata (Optional)**:
   Create `metadata.json`:
   ```json
   {
     "name": "My Custom Model",
     "description": "Description of your model",
     "version": "1.0.0",
     "language": "ne_NP",
     "type": "custom",
     "quality": "high",
     "speakers": 18,
     "sample_rate": 22050,
     "model_size_mb": 75.0,
     "training_data": "Your training dataset",
     "recommended_for": "Specific use case"
   }
   ```

4. **Refresh Models**:
   The system automatically detects new models on startup, or use:
   ```bash
   curl -X POST http://localhost:8000/models/refresh
   ```

## 🌐 API Endpoints

### Model Management
- `GET /models` - List all available models
- `POST /models/{model_id}/load` - Load a specific model
- `POST /models/refresh` - Refresh model list

### TTS Operations
- `POST /synthesize` - Generate speech from text
- `GET /synthesize` - Generate speech (GET method)
- `GET /voice/info` - Get current voice information
- `GET /speakers` - Get available speakers

### Health Check
- `GET /` - API status and current model info

## 🎨 Frontend Features

- **Model Selection**: Radio buttons with model information
- **Quality Indicators**: Visual badges for model quality and type
- **Current Model Highlight**: Shows which model is currently active
- **Model Details**: Size, speakers, training epochs, and descriptions
- **Real-time Switching**: Instant model switching without page reload

## 🔄 Model Conversion

To convert your trained checkpoints to ONNX:

```bash
cd finetune/piper/src/python
python -m piper_train.export_onnx \
  ../../../training_output/lightning_logs/version_X/checkpoints/epoch=XXXX-step=XXXX.ckpt \
  ../../../your_model.onnx
```

Then organize into the models directory structure.

## 🎵 Testing Models

Test any model with Nepali text:
```bash
curl -X POST "http://localhost:8000/synthesize" \
  -H "Content-Type: application/json" \
  -d '{"text": "नमस्कार, यो एक परीक्षण हो।", "speaker_id": 0}' \
  --output test.wav
```

## 🛠️ Troubleshooting

### Models Not Detected
- Ensure `model.onnx` and `config.json` exist in model directory
- Check file permissions
- Restart the API server

### Model Loading Failed
- Verify ONNX model compatibility
- Check config.json format
- Review API logs for detailed errors

### Frontend Not Updating
- Refresh the page
- Check browser console for errors
- Verify API connection

## 📊 Model Comparison

| Model | Type | Quality | Size | Epochs | Best For |
|-------|------|---------|------|--------|----------|
| Fine-tuned | Custom | High | 73.5MB | 2839 | Production |
| Original Google | Pre-trained | Medium | 73.2MB | - | General use |
| Pretrained Checkpoint | Baseline | Medium | 60.6MB | - | Comparison |

The dynamic model system makes it easy to experiment with different models and deploy the best one for your specific use case!
